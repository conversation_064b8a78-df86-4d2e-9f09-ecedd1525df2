<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact PTech Innovation - Get in Touch</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Contact PTech Innovation for innovative IT solutions and digital transformation services. Get in touch with our expert team for consultations and support.">
    <meta name="keywords" content="contact PTech Innovation, IT services contact, technology consultation, software development inquiry, Africa tech support">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="Contact PTech Innovation - Get in Touch">
    <meta property="og:description" content="Reach out to PowerTech Innovation for all your technology needs and digital transformation requirements.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/contact.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact PTech Innovation">
    <meta name="twitter:description" content="Get in touch with Africa's leading technology solutions provider.">
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/contact.html">
    
    <!-- Structured Data for Google -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ContactPage",
      "mainEntity": {
        "@type": "Organization",
        "name": "PowerTech Innovation",
        "url": "https://ptechin.com",
        "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png",
        "description": "Leading IT solutions provider in Africa offering comprehensive software and technology services.",
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "******-456-7890",
          "contactType": "customer service",
          "availableLanguage": ["English"],
          "email": "<EMAIL>"
        },
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "123 Main St",
          "addressLocality": "City",
          "addressCountry": "Country"
        },
        "sameAs": [
          "https://www.linkedin.com/company/*********/admin/",
          "https://www.facebook.com/profile.php?id=61572286554639",
          "https://x.com/powertechin1"
        ]
      }
    }
    </script>
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        @keyframes logoAnimation {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .logo-container {
            animation: logoAnimation 2s ease-in-out infinite alternate;
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.3s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        /* WhatsApp button animations */
        @keyframes whatsappPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.4);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        @keyframes whatsappBounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .whatsapp-button {
            animation: whatsappPulse 2s infinite;
            transition: all 0.3s ease;
        }

        .whatsapp-button:hover {
            animation: whatsappBounce 0.8s ease infinite;
            box-shadow: 0 8px 15px rgba(37, 211, 102, 0.3);
        }

        .dark .whatsapp-button {
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.6);
        }

        /* FAQ Animations */
        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .faq-content:not(.hidden) {
            max-height: 500px;
            transition: max-height 0.3s ease-in;
        }

        .faq-icon {
            transition: transform 0.3s ease;
        }

        /* Live Chat Widget Animations */
        #live-chat-widget {
            transition: transform 0.3s ease, opacity 0.3s ease;
            transform: translateY(20px);
            opacity: 0;
        }

        #live-chat-widget:not(.hidden) {
            transform: translateY(0);
            opacity: 1;
        }

        /* Typing Indicator Animation */
        .typing-indicator {
            display: flex;
            gap: 4px;
        }

        .typing-indicator span {
            width: 8px;
            height: 8px;
            background: #4f46e5;
            border-radius: 50%;
            animation: typing 1s infinite ease-in-out;
        }

        .typing-indicator span:nth-child(1) {
            animation-delay: 0.1s;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.3s;
        }

        @keyframes typing {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

                .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-10px);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>
<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Navigation -->
<!-- Navigation -->
<nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between items-center h-20">
            <!-- Logo section - Left aligned -->
            <div class="flex-shrink-0">
                <a href="index.html" class="flex items-center space-x-3">
                    <div class="logo-container w-16 h-16">
                        <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PowerTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                    </div>
                    <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                </a>
            </div>

            <!-- Right side container for menu and controls -->
            <div class="flex items-center justify-end flex-1 space-x-8">
                <!-- Desktop menu items -->
                <div class="hidden md:flex items-center space-x-8 ml-auto">
                    <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                    <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                    <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                    <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                    <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                    <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                </div>

                <!-- Controls section -->
                <div class="flex items-center space-x-4">
                    <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                        <!-- Sun icon -->
                        <svg class="w-6 h-6 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <!-- Moon icon -->
                        <svg class="w-6 h-6 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                    </button>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu - Right aligned -->
    <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 shadow-lg fixed top-20 left-0 right-0 z-50">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
            <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
            <a href="services.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
            <a href="charity.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
            <a href="legal.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
            <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
        </div>
    </div>
</nav>

    <!-- Header Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl font-bold text-white mb-6">Contact Us</h1>
            <p class="text-xl text-gray-200 max-w-3xl mx-auto">
                Partner with us to transform your business through innovative technology solutions
            </p>
        </div>
    </section>

    <!-- Contact Content -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Contact Form -->
                <div class="bg-gray-50 dark:bg-gray-700 p-8 rounded-lg">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send us a message</h2>
                    <div class="max-w-3xl mx-auto px-4 py-8 mt-20">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Contact Us</h2>
                        
                        <form id="contactForm" class="space-y-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Name</label>
                                <input type="text" name="name" id="name" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                                <input type="email" name="email" id="email" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Subject</label>
                                <input type="text" name="subject" id="subject"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Message</label>
                                <textarea name="message" id="message" rows="4" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"></textarea>
                            </div>

                            <div>
                                <button type="submit"
                                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                                    Send Message
                                </button>
                            </div>
                        </form>

                        <!-- Alert for success/error messages -->
                        <div id="formAlert" class="mt-4 hidden rounded-md p-4">
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8">
                    <!-- Office Address -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Our Office</h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            34D Mamabridge Kissy freetown
                        </p>
                    </div>

                    <!-- Customer Support -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Customer Support</h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            Email: <a href="mailto:<EMAIL>" class="text-indigo-600 hover:text-indigo-500"><EMAIL></a><br>
                            Phone: <a href="tel:+447360680621" class="text-indigo-600 hover:text-indigo-500">+447360680621</a>
                        </p>
                    </div>

                    <!-- Social Media -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Connect With Us</h3>
                        <div class="flex space-x-4">
                            <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 dark:text-gray-500 dark:hover:text-indigo-400">
                                <span class="sr-only">Twitter</span>
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                                </svg>
                            </a>
                            <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                            <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 dark:text-gray-500 dark:hover:text-indigo-400">
                                <span class="sr-only">Facebook</span>
                                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive FAQ Section -->
    <section class="py-16 bg-gray-50 dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center text-gray-900 dark:text-white mb-12">Frequently Asked Questions</h2>
            <div class="max-w-3xl mx-auto space-y-4">
                <!-- FAQ Item 1 -->
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button class="faq-button w-full flex justify-between items-center p-4 focus:outline-none">
                        <span class="text-lg font-medium text-gray-900 dark:text-white">What services do you offer?</span>
                        <svg class="faq-icon w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-content hidden p-4 pt-0">
                        <p class="text-gray-600 dark:text-gray-300">We offer a comprehensive range of IT services including software development, healthcare IT solutions, education technology, data analysis, and security solutions. Each service is customized to meet your specific needs.</p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button class="faq-button w-full flex justify-between items-center p-4 focus:outline-none">
                        <span class="text-lg font-medium text-gray-900 dark:text-white">How quickly can you respond to inquiries?</span>
                        <svg class="faq-icon w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-content hidden p-4 pt-0">
                        <p class="text-gray-600 dark:text-gray-300">We typically respond to all inquiries within 24 hours during business days. For urgent matters, you can reach us through our WhatsApp support or phone line for immediate assistance.</p>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg">
                    <button class="faq-button w-full flex justify-between items-center p-4 focus:outline-none">
                        <span class="text-lg font-medium text-gray-900 dark:text-white">Do you offer custom solutions?</span>
                        <svg class="faq-icon w-6 h-6 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="faq-content hidden p-4 pt-0">
                        <p class="text-gray-600 dark:text-gray-300">Yes, we specialize in creating custom solutions tailored to your business needs. Our team works closely with you to understand your requirements and develop solutions that perfectly match your objectives.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

   

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit" 
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/profile.php?id=61572286554639&sk=about" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                
                <!-- Services -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Our Solutions</h3>
                <ul class="space-y-3">
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Software Development</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Healthcare IT</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Education Technology</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Data Analysis</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Security Solutions</a></li>
                </ul>
            </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved..
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // FAQ Accordion functionality
        document.addEventListener('DOMContentLoaded', function() {
            const faqButtons = document.querySelectorAll('.faq-button');
            
            faqButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const content = button.nextElementSibling;
                    const icon = button.querySelector('.faq-icon');
                    
                    // Toggle content visibility
                    if (content.classList.contains('hidden')) {
                        // Close all other FAQs first
                        document.querySelectorAll('.faq-content').forEach(item => {
                            if (item !== content) {
                                item.classList.add('hidden');
                                const otherIcon = item.previousElementSibling.querySelector('.faq-icon');
                                otherIcon.style.transform = 'rotate(0deg)';
                            }
                        });

                        // Open this FAQ
                        content.classList.remove('hidden');
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        // Close this FAQ
                        content.classList.add('hidden');
                        icon.style.transform = 'rotate(0deg)';
                    }
                });
            });
        });

        // Live Chat Widget functionality
        document.addEventListener('DOMContentLoaded', function() {
            const chatToggle = document.getElementById('chat-toggle');
            const chatWidget = document.getElementById('live-chat-widget');
            const closeChat = document.getElementById('close-chat');
            const chatForm = document.getElementById('chat-form');
            const chatMessages = document.querySelector('.chat-messages');
            let conversationHistory = [];

            // Check server health before enabling chat
            async function checkServerHealth() {
                try {
                    const response = await fetch('http://localhost:3000/health');
                    if (!response.ok) throw new Error('Server health check failed');
                    return true;
                } catch (error) {
                    console.error('Server health check failed:', error);
                    return false;
                }
            }

            // Toggle chat widget
            chatToggle?.addEventListener('click', async function() {
                const isServerHealthy = await checkServerHealth();
                if (!isServerHealthy) {
                    alert('Chat service is currently unavailable. Please try again later.');
                    return;
                }
                chatWidget.classList.remove('hidden');
                chatToggle.classList.add('hidden');
            });

            // Close chat widget
            closeChat?.addEventListener('click', function() {
                chatWidget.classList.add('hidden');
                chatToggle.classList.remove('hidden');
            });

            // Handle chat form submission
            chatForm?.addEventListener('submit', async function(e) {
                e.preventDefault();
                const input = chatForm.querySelector('input');
                const message = input.value.trim();
                
                if (message) {
                    // Add user message to chat
                    const userMessage = document.createElement('div');
                    userMessage.className = 'flex items-start justify-end';
                    userMessage.innerHTML = `
                        <div class="mr-3 bg-indigo-600 rounded-lg p-3">
                            <p class="text-sm text-white">${message}</p>
                        </div>
                    `;
                    chatMessages.appendChild(userMessage);
                    
                    // Show typing indicator
                    const typingIndicator = document.createElement('div');
                    typingIndicator.className = 'flex items-start typing-indicator-container';
                    typingIndicator.innerHTML = `
                        <div class="flex-shrink-0">
                            <img class="h-10 w-10 rounded-full" src="asset/PowerTech_Logo-removebg-preview.png" alt="Support Agent">
                        </div>
                        <div class="ml-3 bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    `;
                    chatMessages.appendChild(typingIndicator);
                    chatMessages.scrollTop = chatMessages.scrollHeight;

                    try {
                        // Call AI backend
                        const response = await fetch('http://localhost:3000/api/chat', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ message })
                        });

                        if (!response.ok) {
                            const errorData = await response.json();
                            throw new Error(errorData.error || 'Failed to get response from server');
                        }

                        const data = await response.json();
                        
                        // Remove typing indicator
                        chatMessages.removeChild(typingIndicator);

                        // Add AI response
                        const botMessage = document.createElement('div');
                        botMessage.className = 'flex items-start';
                        botMessage.innerHTML = `
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full" src="asset/PowerTech_Logo-removebg-preview.png" alt="Support Agent">
                            </div>
                            <div class="ml-3 bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                                <p class="text-sm text-gray-900 dark:text-white">${data.response}</p>
                            </div>
                        `;
                        chatMessages.appendChild(botMessage);

                    } catch (error) {
                        console.error('Chat error:', error);
                        
                        // Remove tyings indicator
                        chatMessages.removeChild(typingIndicator);
                        
                        // Show error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'flex items-start';
                        errorMessage.innerHTML = `
                            <div class="flex-shrink-0">
                                <img class="h-10 w-10 rounded-full" src="asset/PowerTech_Logo-removebg-preview.png" alt="Support Agent">
                            </div>
                            <div class="ml-3 bg-red-100 dark:bg-red-900 rounded-lg p-3">
                                <p class="text-sm text-red-600 dark:text-red-200">${error.message || 'Sorry, I\'m having trouble connecting right now. Please try again later.'}</p>
                            </div>
                        `;
                        chatMessages.appendChild(errorMessage);
                    }

                    // Clear input and scroll to bottom
                    input.value = '';
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            });
        });

        document.getElementById('contactForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const form = e.target;
            const alertDiv = document.getElementById('formAlert');
            const submitButton = form.querySelector('button[type="submit"]');
            
            // Disable submit button and show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = 'Sending...';
            
            try {
                const formData = new FormData(form);
                const response = await fetch('./process_email.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                // Show alert message
                if (response.ok && result.success) {
                    alertDiv.className = 'mt-4 rounded-md p-4 bg-green-50 text-green-800';
                    alertDiv.textContent = result.message;
                    form.reset();
                } else {
                    alertDiv.className = 'mt-4 rounded-md p-4 bg-red-50 text-red-800';
                    alertDiv.innerHTML = `${result.message}<br><small class="text-xs">${JSON.stringify(result.debug_info || {})}</small>`;
                }
                alertDiv.classList.remove('hidden');
                
            } catch (error) {
                alertDiv.className = 'mt-4 rounded-md p-4 bg-red-50 text-red-800';
                alertDiv.innerHTML = `An error occurred. Please try again later.<br><small class="text-xs">Error: ${error.message}</small>`;
                alertDiv.classList.remove('hidden');
            } finally {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Send Message';
            }
        });
    </script>
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>
</body>
</html> 