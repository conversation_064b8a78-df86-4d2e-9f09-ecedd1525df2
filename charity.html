<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTech Innovation Charity - Making a Difference in Africa</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover PTech Innovation's charitable initiatives and social impact programs across Africa. Learn how we're using technology to make a difference in communities.">
    <meta name="keywords" content="PTech Innovation charity, tech for good, African community development, social impact technology, digital inclusion Africa">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="PTech Innovation Charity - Technology for Social Good">
    <meta property="og:description" content="Join PowerTech Innovation in our mission to create positive social impact through technology across Africa.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/charity.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="PTech Innovation Charity Initiatives">
    <meta name="twitter:description" content="Empowering African communities through technology and innovation.">
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/charity.html">
    
    <!-- Structured Data for Google -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "NGO",
      "name": "PowerTech Innovation Charity",
      "url": "https://ptechin.com/charity.html",
      "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png",
      "description": "PowerTech Innovation's charitable arm dedicated to creating positive social impact through technology in Africa.",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Main St",
        "addressLocality": "City",
        "addressCountry": "Country"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-456-7890",
        "contactType": "customer service",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://www.linkedin.com/company/*********/admin/",
        "https://www.facebook.com/profile.php?id=61572286554639",
        "https://x.com/powertechin1"
      ]
    }
    </script>
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        @keyframes logoAnimation {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .logo-container {
            animation: logoAnimation 2s ease-in-out infinite alternate;
            background-color: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.3s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        @keyframes whatsappBounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .whatsapp-button {
            animation: whatsappPulse 2s infinite;
            transition: all 0.3s ease;
        }

        .whatsapp-button:hover {
            animation: whatsappBounce 0.8s ease infinite;
            box-shadow: 0 8px 15px rgba(37, 211, 102, 0.3);
        }

        .dark .whatsapp-button {
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
        }
                .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-10px);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'orange-money': '#FF6B00'
                    }
                }
            }
        }
    </script>
</head>
<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section - Left aligned -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PowerTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu - Right aligned -->
        <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 shadow-lg fixed top-20 left-0 right-0 z-50">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                <a href="services.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                <a href="charity.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                <a href="legal.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-xl p-8 border border-white/20">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-white mb-6">Empowering Through Education</h1>
                    <p class="text-xl text-gray-200 max-w-3xl mx-auto">
                        Join us in our mission to make quality education accessible to everyone through our free online learning platform.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Platform Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">About Our Online Learning Platform</h2>
                    <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                        PTech Innovation has developed an online learning hub to power the Young Africans Alliance Academy. We don't just offer cutting-edge technology solutions, We also provide a high-quality learning platform that empowers your educational journey. Our mission is to bridge the learning gap and support young people and communities by delivering free and affordable learning resources you can access anytime and from the comfort of your home. For each service we provide, 5% of the proceeds go to the Young Africans Alliance Academy Learning Platform. By choosing our services, you invest in our mission, and you will be remembered as one of those who help make young people reach their dreams
                    </p>
                    <div class="bg-indigo-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Who We Are</h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            The Young African Alliance Academy is a non-profit organisation under the PTech Innovation Initiative. Our mission is to create brighter futures for young people, as we believe that poverty should never hinder anyone from achieving their dreams. Our online platform will offer courses such as A-Level Science, Microsoft Office, Introduction to Computer Science, Engineering, Pre-Medicine, and Pre-Pharmacy. This initiative was inspired by the Ashinaga Africa Initiative and is rooted in a deep understanding of the challenges that orphaned and vulnerable young people face across Sub-Saharan Africa. In addition to academic programs, we provide mentorship, leadership training, and opportunities for social engagement to empower our learners to become agents of change.
                        </p>
                    </div>
                </div>
                <div class="relative">
                    <div class="absolute -inset-4">
                        <div class="w-full h-full mx-auto opacity-30 blur-lg filter bg-gradient-to-r from-indigo-600 to-purple-600"></div>
                    </div>
                    <img src="asset/Elearning_platform.webp" alt="Learning Platform" class="relative rounded-lg shadow-xl">
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="py-16 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Services</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Comprehensive learning solutions designed for everyone</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Comprehensive Course Catalog</h3>
                    <ul class="text-gray-600 dark:text-gray-300 space-y-2">
                        <li>• Academic disciplines</li>
                        <li>• Vocational training</li>
                        <li>• Personal development</li>
                        <li>• Skill development</li>
                    </ul>
                </div>

                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Accessible Anywhere</h3>
                    <ul class="text-gray-600 dark:text-gray-300 space-y-2">
                        <li>• Global access</li>
                        <li>• User-friendly platform</li>
                        <li>• Mobile responsive</li>
                        <li>• Multilingual support</li>
                    </ul>
                </div>

                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-lg p-6">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Interactive Learning</h3>
                    <ul class="text-gray-600 dark:text-gray-300 space-y-2">
                        <li>• Live webinars</li>
                        <li>• Discussion forums</li>
                        <li>• Interactive quizzes</li>
                        <li>• Certified courses</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Who We Serve Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Who We Serve</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Our platform is designed for everyone who wants to learn and grow</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-700 dark:to-gray-600 rounded-lg p-8">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Our Learners</h3>
                    <ul class="space-y-4">
                        <li class="flex items-start space-x-4">
                            <svg class="w-6 h-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">Students seeking supplementary learning materials</span>
                        </li>
                        <li class="flex items-start space-x-4">
                            <svg class="w-6 h-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">Job seekers aiming to acquire new skills</span>
                        </li>
                        <li class="flex items-start space-x-4">
                            <svg class="w-6 h-6 text-indigo-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">Community members eager to learn and grow</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-700 dark:to-gray-600 rounded-lg p-8">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Why We Do It</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">
                        Education transforms lives. We believe that when individuals gain knowledge, they uplift not only themselves but also their families and communities. By removing financial and geographical barriers, we aim to create a world where learning is within everyone's reach.
                    </p>
                    <div class="flex items-center justify-center">
                        <a href="#" class="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300">
                            <span>Learn more about our mission</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- E-Learning Platform Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Free Online Learning Platform</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Access quality education from anywhere, at any time.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Course Card 1 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Web Development</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Learn HTML, CSS, and JavaScript from scratch</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 2 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Digital Marketing</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Master social media and digital marketing strategies</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 3 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Data Science</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Introduction to data analysis and visualization</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

               
                <!-- Course Card 4 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">A-Level Science</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Comprehensive science curriculum for advanced students</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 5 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Microsoft Office</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Master essential productivity software skills</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 6 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Computer Science</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Introduction to programming and computer systems</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 7 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Engineering</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Fundamentals of engineering principles and practice</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 8 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Pre-Medicine</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Preparation for medical school and healthcare careers</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

                <!-- Course Card 9 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Pre-Pharmacy</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">Foundation courses for pharmaceutical studies</p>
                        <a href="#" class="inline-block bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors">Start Learning</a>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Donation Section -->
    <section class="py-16 bg-gradient-to-br from-orange-money/10 to-orange-money/5">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Support Our Mission</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Your contribution helps us provide free education to more people</p>
            </div>

            <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
                <div class="p-8">
                    <div class="flex items-center justify-center mb-8">
                        <img src="asset/yaa.jpeg" alt="Orange Money Logo" class="h-16">
                    </div>
                    
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button class="bg-orange-money text-white py-4 px-6 rounded-lg hover:bg-orange-money/90 transition-colors">
                                <a href="https://www.justgiving.com/crowdfunding/yaacademy?utm_term=Bk2MGgAMv" class="block">Donate  $10</a>      
                            </button>
                            <button class="bg-orange-money text-white py-4 px-6 rounded-lg hover:bg-orange-money/90 transition-colors">
                                <a href="https://www.justgiving.com/crowdfunding/yaacademy?utm_term=Bk2MGgAMv" class="block"> Donate  $50</a>
                            </button>
                            <button class="bg-orange-money text-white py-4 px-6 rounded-lg hover:bg-orange-money/90 transition-colors">
                                <a href="https://www.justgiving.com/crowdfunding/yaacademy?utm_term=Bk2MGgAMv" class="block">Donate $100</a>
                            </button>
                        </div>
                        <div class="relative">
                            <input type="number" placeholder="Enter custom amount" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-money focus:border-orange-money dark:bg-gray-700 dark:text-white">
                            <button class="mt-4 w-full bg-orange-money text-white py-3 px-6 rounded-lg hover:bg-orange-money/90 transition-colors">
                                <a href="https://www.justgiving.com/crowdfunding/yaacademy?utm_term=Bk2MGgAMv" class="block">Donate Custom Amount</a>
                            </button>
                        </div>
                    </div>

                    <div class="mt-8 text-center text-sm text-gray-600 dark:text-gray-400">
                        <p>All donations are secure and encrypted.</p>
                        <p class="font-semibold mt-2"></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Impact</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">See how your support makes a difference</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">1000+</div>
                    <div class="text-gray-600 dark:text-gray-300">Students Enrolled</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">20+</div>
                    <div class="text-gray-600 dark:text-gray-300">Free Courses</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-indigo-600 mb-2">85%</div>
                    <div class="text-gray-600 dark:text-gray-300">Completion Rate</div>
                </div>
            </div>
        </div>
    </section>

     <!-- Footer -->
     <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit" 
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                
                <!-- Services -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Our Solutions</h3>
                <ul class="space-y-3">
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Software Development</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Healthcare IT</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Education Technology</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Data Analysis</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Security Solutions</a></li>
                </ul>
            </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved..
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>

    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>
</body>
</html> 