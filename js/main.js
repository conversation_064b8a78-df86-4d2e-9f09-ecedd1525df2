// Dark mode initialization
(function () {
    // Apply dark mode immediately before DOM loads to prevent flash
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode === 'enabled') {
        document.documentElement.classList.add('dark');
    }

    document.addEventListener('DOMContentLoaded', () => {
        const darkModeToggle = document.getElementById('dark-mode-toggle');
        const html = document.documentElement;

        // Function to set dark mode
        function setDarkMode(isDark) {
            if (isDark) {
                html.classList.add('dark');
                localStorage.setItem('darkMode', 'enabled');
                if (darkModeToggle) {
                    darkModeToggle.innerHTML = `
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    `;
                }
            } else {
                html.classList.remove('dark');
                localStorage.setItem('darkMode', 'disabled');
                if (darkModeToggle) {
                    darkModeToggle.innerHTML = `
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                    `;
                }
            }
        }

        // Set initial state based on localStorage
        setDarkMode(savedMode === 'enabled');

        // Handle dark mode toggle clicks
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => {
                const isDark = !html.classList.contains('dark');
                setDarkMode(isDark);
            });
        }
    });
})();

// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function () {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        function toggleMenu(show) {
            if (show) {
                mobileMenu.classList.remove('hidden');
                mobileMenu.classList.add('show');
                mobileMenuButton.setAttribute('aria-expanded', 'true');
                mobileMenuButton.querySelector('svg').innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                `;
            } else {
                mobileMenu.classList.remove('show');
                mobileMenu.classList.add('hidden');
                mobileMenuButton.setAttribute('aria-expanded', 'false');
                mobileMenuButton.querySelector('svg').innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                `;
            }
        }

        // Handle menu button click
        mobileMenuButton.addEventListener('click', function (e) {
            e.stopPropagation();
            const isHidden = mobileMenu.classList.contains('hidden');
            toggleMenu(isHidden);
        });

        // Close menu when clicking outside
        document.addEventListener('click', function (event) {
            if (!mobileMenuButton.contains(event.target) &&
                !mobileMenu.contains(event.target) &&
                !mobileMenu.classList.contains('hidden')) {
                toggleMenu(false);
            }
        });

        // Close menu when pressing Escape key
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Escape' && !mobileMenu.classList.contains('hidden')) {
                toggleMenu(false);
            }
        });

        // Close menu when clicking a menu item
        mobileMenu.addEventListener('click', function (event) {
            if (event.target.tagName === 'A') {
                toggleMenu(false);
            }
        });
    }
});

// Code animation in hero section
const codeSnippet = `function createProject() {
    const project = PTech({
        name: 'Innovation',
        type: 'IT Service'
    });
    
    return project.init();
}

// Thanks for working with us!`;

const codeElement = document.getElementById('code-animation');
let currentIndex = 0;

function typeCode() {
    if (codeElement && currentIndex < codeSnippet.length) {
        codeElement.textContent += codeSnippet.charAt(currentIndex);
        currentIndex++;
        setTimeout(typeCode, 50);
    } else if (codeElement) {
        currentIndex = 0;
        setTimeout(() => {
            codeElement.textContent = '';
            typeCode();
        }, 2000);
    }
}

// Start the animation when the page loads
window.addEventListener('load', typeCode);

// Contact form handling
document.getElementById('contact-form')?.addEventListener('submit', function (e) {
    e.preventDefault();

    // Get form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);

    // Here you would typically send the data to your server
    console.log('Form submitted:', data);

    // Show success message
    alert('Thank you for your message. We will get back to you soon!');

    // Reset form
    this.reset();
});

// Search functionality
const searchInput = document.getElementById('search-input');
const searchResults = document.getElementById('search-results');

const searchData = {
    pages: [
        { title: 'Home', url: '/', keywords: ['home', 'main', 'landing'] },
        { title: 'About Us', url: '/about.html', keywords: ['about', 'company', 'team'] },
        { title: 'Services', url: '/services.html', keywords: ['services', 'offerings', 'solutions'] },
        // Add more pages
    ]
};

searchInput?.addEventListener('input', (e) => {
    const query = e.target.value.toLowerCase();
    if (query.length < 2) {
        searchResults.classList.add('hidden');
        return;
    }

    const matches = searchData.pages.filter(page =>
        page.title.toLowerCase().includes(query) ||
        page.keywords.some(keyword => keyword.includes(query))
    );

    displaySearchResults(matches);
});

function displaySearchResults(matches) {
    if (!searchResults) return;

    searchResults.innerHTML = '';
    searchResults.classList.remove('hidden');

    if (matches.length === 0) {
        searchResults.innerHTML = '<p class="p-4 text-gray-500">No results found</p>';
        return;
    }

    matches.forEach(match => {
        const link = document.createElement('a');
        link.href = match.url;
        link.className = 'block p-4 hover:bg-gray-50 dark:hover:bg-gray-800';
        link.textContent = match.title;
        searchResults.appendChild(link);
    });
}

// Cookie consent
function showCookieConsent() {
    if (localStorage.getItem('cookieConsent')) return;

    const consentBanner = document.createElement('div');
    consentBanner.className = 'fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4';
    consentBanner.innerHTML = `
        <div class="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-between">
            <p class="mb-4 sm:mb-0">
                We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.
            </p>
            <div class="flex space-x-4">
                <button id="accept-cookies" 
                    class="bg-indigo-600 hover:bg-indigo-700 px-4 py-2 rounded-md">
                    Accept
                </button>
                <button id="decline-cookies"
                    class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-md">
                    Decline
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(consentBanner);

    document.getElementById('accept-cookies')?.addEventListener('click', () => {
        localStorage.setItem('cookieConsent', 'accepted');
        consentBanner.remove();
    });

    document.getElementById('decline-cookies')?.addEventListener('click', () => {
        localStorage.setItem('cookieConsent', 'declined');
        consentBanner.remove();
    });
}

window.addEventListener('load', showCookieConsent);

// Page loading animation
document.addEventListener('DOMContentLoaded', () => {
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('page-transition');
        setTimeout(() => {
            mainContent.classList.add('page-loaded');
        }, 100);
    }
});

window.addEventListener('scroll', () => {
    const nav = document.querySelector('nav');
    if (window.scrollY > 100) {
        nav.classList.add('nav-scrolled');
    } else {
        nav.classList.remove('nav-scrolled');
    }
});

// Solution Finder functionality
document.addEventListener('DOMContentLoaded', () => {
    const stepsContainer = document.querySelector('.steps-container');
    const prevButton = document.getElementById('prevStep');
    const nextButton = document.getElementById('nextStep');
    let currentStep = 1;
    const totalSteps = document.querySelectorAll('.step').length;
    const selections = {};

    // Handle option selection
    stepsContainer?.addEventListener('click', (e) => {
        if (e.target.classList.contains('option')) {
            // Remove active state from other options in the same step
            const currentOptions = e.target.closest('.options').querySelectorAll('.option');
            currentOptions.forEach(opt => opt.classList.remove('bg-indigo-50', 'border-indigo-500', 'dark:bg-indigo-900'));

            // Add active state to selected option
            e.target.classList.add('bg-indigo-50', 'border-indigo-500', 'dark:bg-indigo-900');

            // Store selection
            selections[currentStep] = e.target.textContent;

            // Enable next button if an option is selected
            nextButton.classList.remove('opacity-50', 'cursor-not-allowed');
            nextButton.disabled = false;
        }
    });

    // Handle next button click
    nextButton?.addEventListener('click', () => {
        if (currentStep < totalSteps) {
            // Hide current step
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');

            // Show next step
            currentStep++;
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('active');

            // Show previous button
            prevButton.classList.remove('hidden');

            // Update next button text on last step
            if (currentStep === totalSteps) {
                nextButton.textContent = 'Get Recommendations';
                nextButton.addEventListener('click', showRecommendations, { once: true });
            }

            // Disable next button until an option is selected
            if (!selections[currentStep]) {
                nextButton.classList.add('opacity-50', 'cursor-not-allowed');
                nextButton.disabled = true;
            }
        }
    });

    // Handle previous button click
    prevButton?.addEventListener('click', () => {
        if (currentStep > 1) {
            // Hide current step
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.remove('active');

            // Show previous step
            currentStep--;
            document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('active');

            // Hide previous button on first step
            if (currentStep === 1) {
                prevButton.classList.add('hidden');
            }

            // Reset next button if coming back from last step
            if (currentStep < totalSteps) {
                nextButton.textContent = 'Next';
                nextButton.classList.remove('opacity-50', 'cursor-not-allowed');
                nextButton.disabled = false;
            }
        }
    });

    // Function to show recommendations
    function showRecommendations() {
        const recommendationsHtml = `
            <div class="bg-indigo-50 dark:bg-gray-700 p-6 rounded-lg">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Your Recommended Solution</h3>
                <div class="space-y-4">
                    <p class="text-gray-600 dark:text-gray-300">Based on your selections:</p>
                    <ul class="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
                        <li>Industry: ${selections[1]}</li>
                        <li>Challenge: ${selections[2]}</li>
                        <li>Organization Size: ${selections[3]}</li>
                        <li>Timeline: ${selections[4]}</li>
                    </ul>
                    <div class="mt-6">
                        <a href="/contact.html" class="inline-block px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                            Contact Us for Details
                        </a>
                    </div>
                </div>
            </div>
        `;

        // Replace steps with recommendations
        stepsContainer.innerHTML = recommendationsHtml;

        // Hide navigation buttons
        prevButton.classList.add('hidden');
        nextButton.classList.add('hidden');
    }
});

// Add to main.js
window.addEventListener('scroll', () => {
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrolled = (winScroll / height) * 100;
    document.getElementById("progressBar").style.width = scrolled + "%";
});

// Add to main.js
window.addEventListener('load', () => {
    const loader = document.querySelector('.loader');
    loader.style.opacity = '0';
    setTimeout(() => {
        loader.style.display = 'none';
    }, 500);
});

// Add to main.js
particlesJS('particles-js',
    {
        "particles": {
            "number": {
                "value": 80,
                "density": {
                    "enable": true,
                    "value_area": 800
                }
            },
            "color": {
                "value": "#ffffff"
            },
            "opacity": {
                "value": 0.5
            },
            // Add more configuration as needed
        }
    }
);

function toggleServiceDetails(button) {
    // Get the parent service card
    const serviceCard = button.closest('.service-card');

    // Get the details section using a more specific selector
    const details = serviceCard.querySelector('.service-details');

    // Get the arrow icon
    const arrow = button.querySelector('svg');

    if (details) {
        details.classList.toggle('hidden');
        // Animate the arrow
        arrow.style.transform = details.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(180deg)';
    }
}

// Add event listeners when document loads
document.addEventListener('DOMContentLoaded', () => {
    const learnMoreButtons = document.querySelectorAll('.learn-more-btn');
    learnMoreButtons.forEach(button => {
        button.addEventListener('click', () => toggleServiceDetails(button));
    });
});

// FAQ Accordion functionality
document.addEventListener('DOMContentLoaded', function () {
    const faqButtons = document.querySelectorAll('.faq-button');

    if (faqButtons) {
        faqButtons.forEach(button => {
            button.addEventListener('click', () => {
                const content = button.nextElementSibling;
                const icon = button.querySelector('.faq-icon');

                // Toggle content visibility with animation
                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    content.style.maxHeight = content.scrollHeight + 'px';
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    content.style.maxHeight = '0px';
                    setTimeout(() => {
                        content.classList.add('hidden');
                    }, 200);
                    icon.style.transform = 'rotate(0deg)';
                }

                // Close other FAQs
                faqButtons.forEach(otherButton => {
                    if (otherButton !== button) {
                        const otherContent = otherButton.nextElementSibling;
                        const otherIcon = otherButton.querySelector('.faq-icon');
                        otherContent.style.maxHeight = '0px';
                        setTimeout(() => {
                            otherContent.classList.add('hidden');
                        }, 200);
                        otherIcon.style.transform = 'rotate(0deg)';
                    }
                });
            });
        });
    }
});

// Live Chat Widget functionality
document.addEventListener('DOMContentLoaded', function () {
    const chatToggle = document.getElementById('chat-toggle');
    const chatWidget = document.getElementById('live-chat-widget');
    const closeChat = document.getElementById('close-chat');
    const chatForm = document.getElementById('chat-form');
    const chatMessages = document.querySelector('.chat-messages');

    if (chatToggle && chatWidget && closeChat && chatForm && chatMessages) {
        // Toggle chat widget
        chatToggle.addEventListener('click', () => {
            chatWidget.classList.remove('hidden');
            chatToggle.classList.add('hidden');
            // Add entrance animation
            chatWidget.style.transform = 'translateY(0)';
            chatWidget.style.opacity = '1';
        });

        // Close chat widget
        closeChat.addEventListener('click', () => {
            // Add exit animation
            chatWidget.style.transform = 'translateY(20px)';
            chatWidget.style.opacity = '0';
            setTimeout(() => {
                chatWidget.classList.add('hidden');
                chatToggle.classList.remove('hidden');
            }, 300);
        });

        // Handle chat form submission
        chatForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const input = chatForm.querySelector('input');
            const message = input.value.trim();

            if (message) {
                // Add user message
                const userMessage = document.createElement('div');
                userMessage.className = 'flex items-start justify-end';
                userMessage.innerHTML = `
                    <div class="mr-3 bg-indigo-600 rounded-lg p-3">
                        <p class="text-sm text-white">${message}</p>
                    </div>
                `;
                chatMessages.appendChild(userMessage);

                // Add automated response with typing indicator
                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'flex items-start';
                typingIndicator.innerHTML = `
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full" src="asset/PowerTech_Logo-removebg-preview.png" alt="Support Agent">
                    </div>
                    <div class="ml-3 bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                chatMessages.appendChild(typingIndicator);
                chatMessages.scrollTop = chatMessages.scrollHeight;

                // Replace typing indicator with response after delay
                setTimeout(() => {
                    chatMessages.removeChild(typingIndicator);
                    const botMessage = document.createElement('div');
                    botMessage.className = 'flex items-start';
                    botMessage.innerHTML = `
                        <div class="flex-shrink-0">
                            <img class="h-10 w-10 rounded-full" src="asset/PowerTech_Logo-removebg-preview.png" alt="Support Agent">
                        </div>
                        <div class="ml-3 bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                            <p class="text-sm text-gray-900 dark:text-white">Thank you for your message. Our team will get back to you soon!</p>
                        </div>
                    `;
                    chatMessages.appendChild(botMessage);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }, 1500);

                // Clear input
                input.value = '';
            }
        });
    }
});

// Newsletter subscription form handling
document.addEventListener('DOMContentLoaded', function () {
    const newsletterForms = document.querySelectorAll('form.newsletter-form');

    newsletterForms.forEach(form => {
        form.addEventListener('submit', async function (e) {
            e.preventDefault();

            const emailInput = form.querySelector('input[type="email"]');
            const submitButton = form.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;

            // Disable form while submitting
            emailInput.disabled = true;
            submitButton.disabled = true;
            submitButton.textContent = 'Subscribing...';

            try {
                const formData = new FormData();
                formData.append('email', emailInput.value);

                const response = await fetch('process_subscribe.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // Show success/error message
                alert(result.message);

                if (result.success) {
                    // Clear form on success
                    form.reset();
                }
            } catch (error) {
                alert('An error occurred. Please try again later.');
                console.error('Subscription error:', error);
            } finally {
                // Re-enable form
                emailInput.disabled = false;
                submitButton.disabled = false;
                submitButton.textContent = originalButtonText;
            }
        });
    });
});