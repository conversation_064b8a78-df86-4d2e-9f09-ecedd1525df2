<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Services - PTech Innovation | Professional IT Services</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Professional IT services including consulting, development, support, and digital transformation solutions for African businesses.">
    <meta name="keywords" content="IT services, consulting, development, support, digital transformation, African IT solutions">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Our Services - PTech Innovation">
    <meta property="og:description" content="Professional IT services and consulting for African businesses.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/services.html">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Reuse the same styles from products.html for consistency */
        @keyframes logoFloat {
            0% { transform: translateY(100%); opacity: 0; }
            20%, 80% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100%); opacity: 0; }
        }

        @keyframes textSlide {
            0% { transform: translateY(100%); opacity: 0; }
            20%, 80% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100%); opacity: 0; }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover { transform: scale(1.05); }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.1s ease;
        }

        .logo-text:hover { color: #4f46e5; }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-contact {
            animation: whatsappPulse 2s ease-in-out infinite;
            transition: transform 0.3s ease;
            background-color: #25d366 !important;
            border-radius: 50%;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .floating-contact:hover {
            transform: scale(1.2);
            background-color: #22c15e !important;
        }

        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after { width: 100%; }

        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) { display: block; }
        #mobile-menu.hidden { display: none; }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 9999;
            transition: width 0.3s ease;
        }
    </style>

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {}
                }
            }
        }
    </script>
</head>

<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Progress bar -->
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-indigo-600 dark:text-indigo-400 font-semibold">Services</a>
                        <a href="products.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Products</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-indigo-600 dark:text-indigo-400 font-semibold">Services</a>
                <a href="products.html" class="block text-gray-600 dark:text-gray-300">Products</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                    Our Services
                </h1>
                <p class="mt-3 max-w-md mx-auto text-lg text-gray-200 sm:text-xl md:mt-5 md:max-w-3xl">
                    Professional IT services and consulting solutions to transform your business
                </p>
                <div class="mt-10">
                    <a href="#services-grid" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 transition-colors">
                        Explore Our Services
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Grid -->
    <section id="services-grid" class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Professional IT Services</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Comprehensive technology solutions for your business needs</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- IT Consulting -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="text-indigo-600 dark:text-indigo-400 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">IT Consulting</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Strategic technology consulting to optimize your IT infrastructure and drive digital transformation.</p>
                    <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                        <li>• Technology Strategy</li>
                        <li>• Digital Transformation</li>
                        <li>• Infrastructure Planning</li>
                        <li>• Process Optimization</li>
                    </ul>
                </div>

                <!-- Custom Development -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="text-indigo-600 dark:text-indigo-400 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Custom Development</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Bespoke software solutions tailored to your specific business requirements and workflows.</p>
                    <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                        <li>• Web Applications</li>
                        <li>• Mobile Apps</li>
                        <li>• Desktop Software</li>
                        <li>• API Development</li>
                    </ul>
                </div>

                <!-- Cloud Solutions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
                    <div class="text-indigo-600 dark:text-indigo-400 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">Cloud Solutions</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">Cloud migration, deployment, and management services for scalable and secure operations.</p>
                    <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                        <li>• Cloud Migration</li>
                        <li>• Infrastructure as Code</li>
                        <li>• DevOps Implementation</li>
                        <li>• Cloud Security</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-8">Ready to Get Started?</h2>
            <p class="text-xl text-gray-200 mb-8">Let's discuss how our services can help your business grow</p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="contact.html" class="inline-block px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                    Contact Us Today
                </a>
                <a href="products.html" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-indigo-600 transition-colors">
                    View Our Products
                </a>
            </div>
        </div>
    </section>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button class="floating-contact">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>

    <script src="js/main.js"></script>
</body>
</html>
