<!DOCTYPE html>
<html lang="en" >
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PTech Innovation - Leading IT Solutions Provider in Africa</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="PTech Innovation is a leading technology company providing innovative IT solutions, software development, and digital transformation services across Africa.">
    <meta name="keywords" content="PTech Innovation, IT solutions Africa, software development, digital transformation, technology services, African tech company">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="PTech Innovation - Leading IT Solutions Provider in Africa">
    <meta property="og:description" content="Innovative IT solutions and digital transformation services for businesses across Africa.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="PTech Innovation - Leading IT Solutions Provider">
    <meta name="twitter:description" content="Transforming businesses through innovative technology solutions across Africa.">
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/">
    
    <!-- Structured Datas fo Google -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "PowerTech Innovation",
      "url": "https://ptechin.com",
      "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png",
      "description": "Leading IT solutions provider in Africa offering comprehensive software and technology services.",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "123 Main St",
        "addressLocality": "City",
        "addressCountry": "Country"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-456-7890",
        "contactType": "customer service"
      },
      "sameAs": [
        "https://www.linkedin.com/company/*********/admin/",
        "https://www.facebook.com/profile.php?id=61572286554639",
        "https://x.com/powertechin1"
      ]
    }
    </script>
    
    <!-- Website Schema Markup -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "PTech Innovation",
      "url": "https://ptechin.com",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://ptechin.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    
    <style>
        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.1s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-contact {
            animation: whatsappPulse 2s ease-in-out infinite;
            transition: transform 0.3s ease;
            background-color: #25d366 !important;
            border-radius: 50%;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .floating-contact:hover {
            transform: scale(1.2);
            background-color: #22c15e !important;
        }

        .dark .floating-contact {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .dark .floating-contact:hover {
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4);
        }

            
        /* Navigation styles */
        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) {
            display: block;
        }

        #mobile-menu.hidden {
            display: none;
        }

    </style>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Montserrat:wght@400;600&family=Open+Sans:wght@400;600&family=Playfair+Display:wght@400;600&family=Roboto:wght@400;500&family=Source+Sans+Pro:wght@400;600&display=swap" rel="stylesheet">
    <!-- Particles.js -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>

<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Add after opening body tag -->
<div class="progress-bar" id="progressBar"></div>
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section - Left aligned -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="products.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Products</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-gray-600 dark:text-gray-300">Services</a>
                <a href="products.html" class="block text-gray-600 dark:text-gray-300">Products</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600 relative">
        <div id="particles-js" class="absolute inset-0 z-0"></div>
        <div class="relative z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="lg:flex lg:items-center lg:justify-between">
                    <div class="lg:w-1/2">
                        <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5lg md:text-6xl">
                            Transforming Africa Through Technology
                        </h1>
                        <p class="mt-3 max-w-md mx-auto text-lg text-gray-200 sm:text-xl md:mt-5 md:max-w-3xl">
                            Empowering businesses and communities with innovative technology solutions for a sustainable future in Africa.
                        </p>
                        <div class="mt-10">
                            <a href="services.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50">
                                Explore Our Solutions
                            </a>
                        </div>
                    </div>
                    <div class="mt-10 lg:mt-0 lg:w-1/2">
                        <!-- Animated code display -->
                        <div class="bg-gray-900 rounded-lg p-4 font-mono text-sm text-gray-200">
                            <pre id="code-animation" class="overflow-hidden"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Overview Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="relative">
                <!-- Background Card with Shadow and Border -->
                <div class="absolute inset-0 transform -skew-y-2 bg-white dark:bg-gray-700 rounded-3xl shadow-2xl"></div>
                
                <!-- Content Container -->
                <div class="relative max-w-4xl mx-auto bg-white dark:bg-gray-700 rounded-3xl shadow-xl overflow-hidden">
                    <!-- Decorative Top Border -->
                    <div class="h-2 bg-gradient-to-r from-indigo-500 to-purple-500"></div>
                    
                    <!-- Content Wrapper -->
                    <div class="px-8 py-12 sm:px-12">
                        <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-8 font-['Playfair_Display'] text-center">
                            Pioneering Change
                        </h2>
                        
                        <div class="relative">
                            <!-- Decorative elements -->
                            <div class="absolute -left-4 -top-4 w-8 h-8 text-indigo-500 opacity-20">
                                <svg fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                                </svg>
                            </div>
                            <div class="absolute -right-4 -bottom-4 w-8 h-8 text-indigo-500 opacity-20 transform rotate-180">
                                <svg fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z"/>
                                </svg>
                            </div>

                            <!-- Overview text -->
                            <div class="space-y-6">
                                <p class="text-lg leading-relaxed text-gray-700 dark:text-gray-300 font-['Inter']">
                                    <span class="font-semibold text-indigo-600 dark:text-indigo-400">PTech Innovation</span> is a start-up social enterprise dedicated to advancing Sierra Leone and across Africa through innovative technology. Our unique approach combines cutting-edge innovation, consulting, and a focus on serving our country through technology solutions.
                                </p>
                                <p class="text-lg leading-relaxed text-gray-700 dark:text-gray-300 font-['Inter']">
                                    This allows us to provide reliable tools for efficient and dependable services, such as our state-of-the-art software, IT solutions, consulting and database services. We are committed to delivering these services across various sectors, including healthcare, academia, and finance, to address critical challenges and create sustainable solutions that benefit the communities we serve.
                                </p>
                            </div>
                        </div>

                        <!-- Key Features Grid -->
                        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="p-4 rounded-lg bg-indigo-50 dark:bg-gray-600">
                                <div class="text-indigo-600 dark:text-indigo-400 mb-2">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                    </svg>
                                </div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Innovation</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Cutting-edge technology solutions</p>
                            </div>
                            <div class="p-4 rounded-lg bg-purple-50 dark:bg-gray-600">
                                <div class="text-purple-600 dark:text-purple-400 mb-2">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Consulting</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Expert guidance and support</p>
                            </div>
                            <div class="p-4 rounded-lg bg-indigo-50 dark:bg-gray-600">
                                <div class="text-indigo-600 dark:text-indigo-400 mb-2">
                                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                </div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">Solutions</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-300">Sustainable community impact</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Focus Areas Section (Add before Solution Finder Section) -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Our Focus Areas</h2>
                <p class="mt-4 text-lg text-gray-600 dark:text-gray-300">Addressing Africa's Key Technology Challenges</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Digital Healthcare</h3>
                    <p class="text-gray-600 dark:text-gray-300">Improving healthcare accessibility through telemedicine and digital health records management.</p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">EdTech Solutions</h3>
                    <p class="text-gray-600 dark:text-gray-300">Bridging the education gap with innovative learning platforms and digital resources.</p>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                    <div class="text-indigo-600 mb-4">
                        <svg class="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Financial Inclusion</h3>
                    <p class="text-gray-600 dark:text-gray-300">Enabling access to digital financial services and mobile banking solutions.</p>
                </div>
            </div>
        </div>
    </section>

    

    <!-- Solution Finder Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12 text-gray-900 dark:text-white">Find Your Perfect Solution</h2>
            <div class="solution-finder">
                <div class="steps-container">
                    <div class="step active" data-step="1">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">What's your industry?</h3>
                        <div class="options">
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Healthcare</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Education</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Finance</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Agriculture</button>
                        </div>
                    </div>

                    <div class="step" data-step="2">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">What's your primary challenge?</h3>
                        <div class="options">
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Digital Transformation</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Data Management</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Process Automation</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Security & Compliance</button>
                        </div>
                    </div>

                    <div class="step" data-step="3">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">What's your organization size?</h3>
                        <div class="options">
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Small (1-50 employees)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Medium (51-200 employees)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Large (201-1000 employees)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Enterprise (1000+ employees)</button>
                        </div>
                    </div>

                    <div class="step" data-step="4">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">What's your timeline?</h3>
                        <div class="options">
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Immediate (1-3 months)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Short-term (3-6 months)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Medium-term (6-12 months)</button>
                            <button class="option dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-indigo-500">Long-term (12+ months)</button>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button id="prevStep" class="px-6 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 hidden">Previous</button>
                    <button id="nextStep" class="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 ml-auto">Next</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Success Stories</h2>
                <p class="mt-4 text-lg text-gray-600 dark:text-gray-300">Transforming African Businesses Through Innovation</p>
            </div>
            
            <div class="mt-12 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                <!-- Testimonial 1 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="h-12 w-12 rounded-full bg-indigo-500 flex items-center justify-center">
                            <span class="text-xl text-white font-bold">DM</span>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Dr. Makena</h4>
                            <p class="text-gray-600 dark:text-gray-300">Medical Director, Nairobi Health Center</p>
                        </div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">"PTech's healthcare management system has revolutionized our patient care. We've reduced wait times by 60% and improved treatment tracking significantly."</p>
                    <div class="mt-4 flex text-indigo-500">
                        ★★★★★
                    </div>
                </div>

                <!-- Testimonial 2 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="h-12 w-12 rounded-full bg-purple-500 flex items-center justify-center">
                            <span class="text-xl text-white font-bold">CA</span>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Chioma Adebayo</h4>
                            <p class="text-gray-600 dark:text-gray-300">CEO, AgriTech Solutions</p>
                        </div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">"The data analytics platform has transformed how we support local farmers. We've helped over 10,000 farmers increase their crop yields through data-driven decisions."</p>
                    <div class="mt-4 flex text-indigo-500">
                        ★★★★★
                    </div>
                </div>

                <!-- Testimonial 3 -->
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="h-12 w-12 rounded-full bg-green-500 flex items-center justify-center">
                            <span class="text-xl text-white font-bold">KD</span>
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Kwame Diallo</h4>
                            <p class="text-gray-600 dark:text-gray-300">Founder, EduTech Ghana</p>
                        </div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300">"PTech's e-learning platform has enabled us to reach students in remote areas. We've connected over 50,000 students with quality education resources."</p>
                    <div class="mt-4 flex text-indigo-500">
                        ★★★★★
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section (Add before Footer) -->
    <section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-8">Ready to Transform Your Business?</h2>
            <p class="text-xl text-gray-200 mb-8">Join the digital revolution in Africa with PTech Innovation</p>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
                <a href="contact.html" class="inline-block px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                    Contact Us
                </a>
                <a href="services.html" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-indigo-600 transition-colors">
                    Explore Solutions
                </a>
            </div>
        </div>
    </section>

   

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit" 
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z">
                                </path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                
                <!-- Services -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Our Solutions</h3>
                <ul class="space-y-3">
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Software Development</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Healthcare IT</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Education Technology</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Data Analysis</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Security Solutions</a></li>
                </ul>
            </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved..
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <script src="js/main.js"></script>

    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>
</body>
</html> 