<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legal Information - PTech Innovation Terms and Privacy Policy</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="PowerTech Innovation's legal information, terms of service, privacy policy, and data protection guidelines. Learn about our commitment to protecting your rights and data.">
    <meta name="keywords" content="PTech Innovation legal, terms of service, privacy policy, data protection, IT services terms, Africa tech legal">
    <meta name="author" content="PowerTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="Legal Information - PTech Innovation">
    <meta property="og:description" content="Important legal information about PowerTech Innovation's services, privacy policy, and terms of use.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/legal.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Legal Information - PTech Innovation">
    <meta name="twitter:description" content="Read our terms of service and privacy policy to understand how we protect your rights and data.">
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/legal.html">
    
    <!-- Structured Data for Google -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Legal Information - PowerTech Innovation",
      "description": "Important legal information, terms of service, and privacy policy for PowerTech Innovation's services.",
      "publisher": {
        "@type": "Organization",
        "name": "PowerTech Innovation",
        "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png"
      },
      "url": "https://ptechin.com/legal.html",
      "mainEntity": {
        "@type": "Article",
        "name": "Terms of Service and Privacy Policy",
        "author": {
          "@type": "Organization",
          "name": "PowerTech Innovation"
        },
        "publisher": {
          "@type": "Organization",
          "name": "PowerTech Innovation",
          "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png"
        }
      }
    }
    </script>
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        @keyframes logoAnimation {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .logo-container {
            animation: logoAnimation 2s ease-in-out infinite alternate;
            background-color: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.3s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        @keyframes whatsappBounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .whatsapp-button {
            animation: whatsappPulse 2s infinite;
            transition: all 0.3s ease;
        }

        .whatsapp-button:hover {
            animation: whatsappBounce 0.8s ease infinite;
            box-shadow: 0 8px 15px rgba(37, 211, 102, 0.3);
        }

        .dark .whatsapp-button {
            box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
        }

                .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-10px);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>
<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Navigation -->
   <!-- Navigation -->
   <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between items-center h-20">
            <!-- Logo section - Left aligned -->
            <div class="flex-shrink-0">
                <a href="index.html" class="flex items-center space-x-3">
                    <div class="logo-container w-16 h-16">
                        <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PowerTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                    </div>
                    <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                </a>
            </div>

            <!-- Right side container for menu and controls -->
            <div class="flex items-center justify-end flex-1 space-x-8">
                <!-- Desktop menu items -->
                <div class="hidden md:flex items-center space-x-8 ml-auto">
                    <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                    <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                    <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                    <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                    <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                    <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                </div>

                <!-- Controls section -->
                <div class="flex items-center space-x-4">
                    <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                        <!-- Sun icon -->
                        <svg class="w-6 h-6 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <!-- Moon icon -->
                        <svg class="w-6 h-6 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                    </button>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu - Right aligned -->
    <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 shadow-lg fixed top-20 left-0 right-0 z-50">
        <div class="px-2 pt-2 pb-3 space-y-1">
            <a href="index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
            <a href="about.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
            <a href="services.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
            <a href="charity.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
            <a href="legal.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
            <a href="contact.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
        </div>
    </div>
</nav>

    <!-- Header Section with Animated Background -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="absolute inset-0 bg-pattern opacity-10"></div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
            <h1 class="text-4xl font-bold text-white mb-6">Legal Information</h1>
            <p class="text-xl text-gray-200 max-w-3xl mx-auto">
                Important policies and terms governing our technology solutions and services
            </p>
        </div>
    </section>

    <!-- Interactive Legal Content -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Legal Navigation Tabs -->
            <div class="mb-12">
                <div class="border-b border-gray-200 dark:border-gray-700">
                    <nav class="-mb-px flex space-x-8" aria-label="Legal Navigation">
                        <button onclick="showTab('privacy')" class="legal-tab active border-b-2 border-indigo-500 py-4 px-1 text-sm font-medium text-indigo-600 dark:text-indigo-400" data-tab="privacy">
                            Privacy Policy
                        </button>
                        <button onclick="showTab('terms')" class="legal-tab border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300" data-tab="terms">
                            Terms of Service
                        </button>
                        <button onclick="showTab('copyright')" class="legal-tab border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300" data-tab="copyright">
                            Copyright
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Legal Content Sections -->
            <div class="max-w-4xl mx-auto">
                <!-- Privacy Policy Section -->
                <div id="privacy" class="legal-content active space-y-8">
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 transform transition-all duration-300 hover:shadow-xl">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Privacy Policy</h2>
                        
                        <!-- Interactive Accordion for Privacy Policy -->
                        <div class="space-y-4">
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Data Collection</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        We collect information necessary to provide our technology solutions and services, including:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Personal information provided during account creation</li>
                                        <li>Usage data to improve our services</li>
                                        <li>Technical information for service optimization</li>
                                        <li>Communication records for support purposes</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Data Usage</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        We use collected information to:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Deliver and improve our services</li>
                                        <li>Develop innovative solutions</li>
                                        <li>Ensure security of operations</li>
                                        <li>Enhance user experience</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Data Protection</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        Our security measures include:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>End-to-end encryption</li>
                                        <li>Regular security audits</li>
                                        <li>Access controls and monitoring</li>
                                        <li>Compliance with international standards</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms of Service Section -->
                <div id="terms" class="legal-content hidden space-y-8">
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 transform transition-all duration-300 hover:shadow-xl">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Terms of Service</h2>
                        
                        <!-- Interactive Accordion for Terms -->
                        <div class="space-y-4">
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Service Terms</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        Our services are provided under these conditions:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Compliance with local laws</li>
                                        <li>Accurate information provision</li>
                                        <li>Responsible service usage</li>
                                        <li>Account security maintenance</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Usage Terms</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        Users are responsible for:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Account security</li>
                                        <li>Content management</li>
                                        <li>Data accuracy</li>
                                        <li>Compliance with terms</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Service Modifications</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        We reserve the right to:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Modify services</li>
                                        <li>Update terms</li>
                                        <li>Adjust pricing</li>
                                        <li>Enhance features</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Copyright Section -->
                <div id="copyright" class="legal-content hidden space-y-8">
                    <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 transform transition-all duration-300 hover:shadow-xl">
                        <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Copyright Information</h2>
                        
                        <!-- Interactive Accordion for Copyright -->
                        <div class="space-y-4">
                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Protected Content</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        Protected content includes:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Website content</li>
                                        <li>Software code</li>
                                        <li>Graphics and images</li>
                                        <li>Documentation</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                                <button class="flex justify-between items-center w-full" onclick="toggleAccordion(this)">
                                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Usage Rights</h3>
                                    <svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </button>
                                <div class="mt-4 hidden">
                                    <p class="text-gray-600 dark:text-gray-300">
                                        Content usage guidelines:
                                    </p>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300 list-disc pl-5">
                                        <li>Personal use only</li>
                                        <li>No redistribution</li>
                                        <li>Attribution required</li>
                                        <li>Modification restrictions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

   <!-- Footer -->
   <footer class="bg-gray-900 text-white">
    <!-- Newsletter Section -->
    <div class="border-b border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="flex flex-col md:flex-row items-center justify-between">
                <div class="mb-6 md:mb-0">
                    <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                    <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                </div>
                <div class="w-full md:w-auto">
                    <form class="newsletter-form flex gap-2">
                        <input type="email" name="email" placeholder="Enter your email" required
                            class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                        <button type="submit" 
                            class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                            Subscribe
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Foter Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            <!-- Company Info -->
            <div>
                <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                <div class="flex space-x-4">
                   <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    <a href="https://www.facebook.com/profile.php?id=61572286554639&sk=about" class="text-gray-400 hover:text-indigo-500 transition-colors">
                        <span class="sr-only">Facebook</span>
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                        </svg>
                    </a>
                    <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                        <span class="sr-only">X (Twitter)</span>
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                <ul class="space-y-3">
                    <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                    <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                    <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                    <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                    <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                </ul>
            </div>

            
            <!-- Service -->
        <div>
            <h3 class="text-lg font-semibold mb-4">Our Solutions</h3>
            <ul class="space-y-3">
                <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Software Development</a></li>
                <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Healthcare IT</a></li>
                <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Education Technology</a></li>
                <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Data Analysis</a></li>
                <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Security Solutions</a></li>
            </ul>
        </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                <ul class="space-y-3">
                    <li class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                    </li>
                    <li class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <span class="text-gray-400"><EMAIL></span>
                    </li>
                    <li class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                        </svg>
                        <span class="text-gray-400">+447360680621</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bottom Bar -->
    <div class="border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-gray-400 text-sm">
                    © 2025 PTech Innovation. All rights reserved..
                </div>
                <div class="flex space-x-6">
                    <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                    <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                    <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                </div>
            </div>
        </div>
    </div>
</footer>

    <script src="js/main.js"></script>
    <script>
        // Tab switching functionality
        function showTab(tabId) {
            // Hide all content sections
            document.querySelectorAll('.legal-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Show selected content
            document.getElementById(tabId).classList.remove('hidden');
            
            // Update tab styles
            document.querySelectorAll('.legal-tab').forEach(tab => {
                tab.classList.remove('border-indigo-500', 'text-indigo-600', 'dark:text-indigo-400');
                tab.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            });
            
            const activeTab = document.querySelector(`[data-tab="${tabId}"]`);
            activeTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
            activeTab.classList.add('border-indigo-500', 'text-indigo-600', 'dark:text-indigo-400');
        }

        // Accordion functionality
        function toggleAccordion(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');
            
            // Toggle content visibility
            content.classList.toggle('hidden');
            
            // Rotate icon
            if (content.classList.contains('hidden')) {
                icon.style.transform = 'rotate(0deg)';
            } else {
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // Initialize first accordion item as open
        document.addEventListener('DOMContentLoaded', () => {
            const firstAccordion = document.querySelector('.legal-content.active .border-b:first-child button');
            if (firstAccordion) {
                toggleAccordion(firstAccordion);
            }
        });
    </script>
   <a href="https://wa.link/eg8s8c" target="_blank">
    <button id="contactButton" class="floating-contact whatsapp-button">
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
            <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
        </svg>
    </button>
</a>
</body>
</html>