<!DOCTYPE html>
<html  lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About PTech Innovation - Our Mission and Vision</title>
    
    <!-- SEO s eta Tags -->
    <meta name="description" content="Learn about PowerTech Innovation's mission to transform Africa through technology. Discover our values, team, and commitment to innovation and excellence.">
    <meta name="keywords" content="PowerTech Innovation about, African tech company, IT company mission, technology vision, digital transformation Africa">
    <meta name="author" content="PowerTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags for Social Media -->
    <meta property="og:title" content="About PowerTech Innovation - Our Mission and Vision">
    <meta property="og:description" content="Discover PowerTech Innovation's journey in transforming Africa through innovative technology solutions.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/about.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="About PTech Innovation - Technology Leaders">
    <meta name="twitter:description" content="Learn about our mission to revolutionize African businesses through innovative technology solutions.">
    <meta name="twitter:image" content="asset/PTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/about.html">
    
    <!-- Structured Data for Google -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "AboutPage",
      "mainEntity": {
        "@type": "Organization",
        "name": "PowerTech Innovation",
        "url": "https://ptechin.com",
        "logo": "https://ptechin.com/asset/PowerTech_Logo-removebg-preview.png",
        "description": "Leading IT solutions provider in Africa offering comprehensive software and technology services.",
        "foundingDate": "2023",
        "founders": [{
          "@type": "Person",
          "name": "PowerTech Innovation Team"
        }],
        "address": {
          "@type": "PostalAddress",
          "streetAddress": "123 Main St",
          "addressLocality": "City",
          "addressCountry": "Country"
        },
        "sameAs": [
          "https://www.linkedin.com/company/*********/admin/",
          "https://www.facebook.com/profile.php?id=61572286554639",
          "https://x.com/powertechin1"
        ]
      }
    }
    </script>
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        @keyframes logoAnimation {
            0% {
                opacity: 0;
                transform: translateY(20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .logo-container {
            animation: logoAnimation 2s ease-in-out infinite alternate;
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.3s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        @keyframes whatsappPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
            }
            70% {
                transform: scale(1.05);
                box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
            }
        }

        @keyframes whatsappBounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .whatsapp-button {
            animation: whatsappPulse 2s infinite;
            transition: all 0.3s ease;
        }

        .whatsapp-button:hover {
            animation: whatsappBounce 0.8s ease infinite;
            box-shadow: 0 8px 15px rgba(37, 211, 102, 0.3);
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) {
            display: block;
        }

        #mobile-menu.hidden {
            display: none;
        }

        .menu-items {
            padding: 1rem;
        }

        .menu-items a {
            display: block;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .menu-items a:hover {
            background-color: #f3f4f6;
            color: #4f46e5;
        }

        .dark .menu-items a:hover {
            background-color: #374151;
            color: #818cf8;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .right-menu {
            margin-left: auto;
            display: flex;
            align-items: center;
        }

        /* Mobile menu animation for right alignment */
        #mobile-menu {
            transform-origin: top right;
        }

        #mobile-menu.show {
            transform: translateY(0) scale(1);
            opacity: 1;
        }

        #mobile-menu.hidden {
            transform: translateY(-10px) scale(0.95);
            opacity: 0;
        }

    </style>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>
<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section - Left aligned -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PowerTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <!-- Sun icon -->
                            <svg class="w-6 h-6 hidden dark:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                            <!-- Moon icon -->
                            <svg class="w-6 h-6 block dark:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-gray-600 dark:text-gray-300">Services</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Mission Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-xl p-8 border border-white/20">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-white mb-6">Vision</h1>
                    <p class="text-xl text-gray-200 max-w-3xl mx-auto">
                        To empower businesses, organizations, and communities across Africa by delivering innovative technology solutions that drive sustainable growth and improve lives.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Journey Timeline -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        <!-- Animated Code tu Background -->
        <div class="absolute inset-0 overflow-hidden">
            <div id="matrix-rain"></div>
        </div>
        
        <style>
            #matrix-rain {
                position: absolute;
                inset: 0;
                background: transparent;
            }

            .matrix-column {
                position: absolute;
                top: -20px;
                font-family: monospace;
                font-size: 14px;
                line-height: 1.2;
                white-space: nowrap;
                color: rgba(99, 102, 241, 0.15);
                text-shadow: 0 0 3px rgba(99, 102, 241, 0.1);
            }

            @keyframes matrix-rain {
                0% {
                    transform: translateY(-100%);
                    opacity: 0.15;
                }
                100% {
                    transform: translateY(100vh);
                    opacity: 0;
                }
            }

            .code-char {
                display: block;
                animation: glow 1.5s ease-in-out infinite alternate;
            }

            @keyframes glow {
                from {
                    text-shadow: 0 0 1px rgba(99, 102, 241, 0.1);
                }
                to {
                    text-shadow: 0 0 4px rgba(99, 102, 241, 0.2);
                }
            }
        </style>

        <script>
            const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789{}[]()<>/*-+~|';
            const codeSnippets = [
                'function()',
                'if(true){',
                'while(1){',
                'for(let i=0;',
                'const data=',
                'return new',
                'async/await',
                'try{catch}',
                '=>{};//',
                'import{}'
            ];

            function createMatrixColumn() {
                const column = document.createElement('div');
                column.className = 'matrix-column';
                
                // Random position and speed
                const x = Math.random() * 100;
                const speed = 5 + Math.random() * 15;
                const delay = -Math.random() * 5;
                
                column.style.left = `${x}%`;
                column.style.animation = `matrix-rain ${speed}s linear ${delay}s infinite`;
                
                // Create content
                let content = '';
                const length = 10 + Math.floor(Math.random() * 20);
                
                for (let i = 0; i < length; i++) {
                    if (Math.random() < 0.3) { // 30% chance for code snippet
                        const snippet = codeSnippets[Math.floor(Math.random() * codeSnippets.length)];
                        content += `<span class="code-char">${snippet}</span>`;
                        i += snippet.length - 1;
                    } else {
                        content += `<span class="code-char">${characters[Math.floor(Math.random() * characters.length)]}</span>`;
                    }
                }
                
                column.innerHTML = content;
                return column;
            }

            function initMatrixRain() {
                const container = document.getElementById('matrix-rain');
                const columnCount = Math.floor(window.innerWidth / 30); // Adjust density
                
                // Initial columns
                for (let i = 0; i < columnCount; i++) {
                    container.appendChild(createMatrixColumn());
                }
                
                // Continuously add new columns
                setInterval(() => {
                    const columns = container.getElementsByClassName('matrix-column');
                    if (columns.length < columnCount) {
                        container.appendChild(createMatrixColumn());
                    }
                    
                    // Clean up old columns
                    Array.from(columns).forEach(column => {
                        const rect = column.getBoundingClientRect();
                        if (rect.top > window.innerHeight * 2) {
                            column.remove();
                        }
                    });
                }, 500);
            }

            // Initialize when document is loaded
            document.addEventListener('DOMContentLoaded', initMatrixRain);
            
            // Reinitialize on window resize
            window.addEventListener('resize', () => {
                const container = document.getElementById('matrix-rain');
                container.innerHTML = '';
                initMatrixRain();
            });
        </script>



        <!-- Mission Section -->
        <section class="py-20 bg-gradient-to-br from-gray-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
            <!-- Decorative Background Elements -->
            <div class="absolute inset-0">
                <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
                <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-full filter blur-3xl transform -translate-x-1/2 translate-y-1/2"></div>
                <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-l from-indigo-500/10 to-purple-500/10 rounded-full filter blur-3xl transform translate-x-1/2 -translate-y-1/2"></div>
            </div>

            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                <div class="max-w-4xl mx-auto">
                    <!-- Mission Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
                        <!-- Top Border Gradient -->
                        <div class="h-2 bg-gradient-to-r from-indigo-500 to-purple-500"></div>
                        
                        <div class="p-8 sm:p-12">
                            <!-- Title -->
                            <div class="flex items-center justify-center mb-8">
                                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                    </svg>
                                </div>
                                <h2 class="text-4xl font-bold text-gray-900 dark:text-white ml-4 font-['Playfair_Display']">Our Mission</h2>
                            </div>

                            <!-- Mission Text -->
                            <div class="space-y-6">
                                <p class="text-lg leading-relaxed text-gray-700 dark:text-gray-300 font-['Inter']">
                                    Our mission is to close the digital gap and deliver cutting-edge software and data services that enhance the efficiency and impact of healthcare, academic,  NGOs and finance  in Sierra Leone and around the continent.
                                </p>
                                <p class="text-lg leading-relaxed text-gray-700 dark:text-gray-300 font-['Inter']">
                                    We are dedicated to fostering societal growth and development through technological innovation, regardless of size or location. With our access to reliable digital tools and data-driven insights, we are improving patient care and operational efficiency, instilling confidence in our capabilities.
                                </p>
                            </div>

                            <!-- Key Focus Areas -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                                <div class="text-center p-4 rounded-lg bg-indigo-50 dark:bg-gray-700">
                                    <span class="text-indigo-600 dark:text-indigo-400 font-semibold">Healthcare</span>
                                </div>
                                <div class="text-center p-4 rounded-lg bg-purple-50 dark:bg-gray-700">
                                    <span class="text-purple-600 dark:text-purple-400 font-semibold">Academia</span>
                                </div>
                                <div class="text-center p-4 rounded-lg bg-indigo-50 dark:bg-gray-700">
                                    <span class="text-indigo-600 dark:text-indigo-400 font-semibold">Finance</span>
                                </div>
                                <div class="text-center p-4 rounded-lg bg-indigo-50 dark:bg-gray-700">
                                    <span class="text-indigo-600 dark:text-indigo-400 font-semibold">NGOs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <!-- Existing Journey Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Our Journey</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">Transforming Africa's technological landscape through innovation, dedication, and sustainable solutions.</p>
            </div>
            
            <div class="relative">
                <!-- Timeline line -->
                <div class="absolute h-full w-1 bg-gradient-to-b from-indigo-500 to-purple-500 left-1/2 md:left-1/2 transform -translate-x-1/2 hidden md:block"></div>
                <!-- Mobile timeline line -->
                <div class="absolute h-full w-1 bg-gradient-to-b from-indigo-500 to-purple-500 left-4 md:hidden"></div>
                
                <!-- Timeline items -->
                <div class="space-y-16">
                    <!-- 2024 Timeline Item -->
                    <div class="relative">
                        <div class="flex flex-col md:flex-row items-start md:items-start justify-between">
                            <div class="w-full md:w-5/12 pl-12 md:pl-0 text-left md:text-right md:pr-8 mb-8 md:mb-0">
                                <h3 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">2024</h3>
                                <h4 class="text-xl font-semibold text-gray-900 dark:text-white mt-2">Company Founded</h4>
                                <p class="text-gray-600 dark:text-gray-300 mt-2">Established with a vision to transform industries across Africa through innovative technology solutions.</p>
                            </div>
                            <div class="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white dark:bg-gray-800 border-4 border-indigo-500 rounded-full"></div>
                            <!-- Mobile timeline dot -->
                            <div class="md:hidden absolute left-0 w-8 h-8 bg-white dark:bg-gray-800 border-4 border-indigo-500 rounded-full"></div>
                            <div class="w-full md:w-5/12 pl-12 md:pl-8">
                                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                                    <h4 class="text-xl font-semibold text-gray-900 dark:text-white">Key Milestones</h4>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300">
                                        <li>• Launched comprehensive software development services</li>
                                        <li>• Established partnerships with healthcare institutions</li>
                                        <li>• Initiated educational technology programs</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2025 Timeline Item -->
                    <div class="relative">
                        <div class="flex flex-col md:flex-row items-start md:items-start justify-between">
                            <div class="w-full md:w-5/12 pl-12 md:pl-0 text-left md:text-right md:pr-8 mb-8 md:mb-0 md:order-1">
                                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
                                    <h4 class="text-xl font-semibold text-gray-900 dark:text-white">Expansion</h4>
                                    <ul class="mt-4 space-y-2 text-gray-600 dark:text-gray-300">
                                        <li>• Extended services to multiple African countries</li>
                                        <li>• Developed innovative healthcare IT solutions</li>
                                        <li>• Implemented advanced data analysis systems</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white dark:bg-gray-800 border-4 border-indigo-500 rounded-full"></div>
                            <!-- Mobile timeline dot -->
                            <div class="md:hidden absolute left-0 w-8 h-8 bg-white dark:bg-gray-800 border-4 border-indigo-500 rounded-full"></div>
                            <div class="w-full md:w-5/12 pl-12 md:pl-8 text-left md:text-left md:order-2">
                                <h3 class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">2025</h3>
                                <h4 class="text-xl font-semibold text-gray-900 dark:text-white mt-2">Growth & Innovation</h4>
                                <p class="text-gray-600 dark:text-gray-300 mt-2">Expanding our reach and impact across Africa while continuously innovating our solutions.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="py-20 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">Passionate individuals working together to create exceptional experiences.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Team Member Card -->
                <div class="group bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <div class="relative">
                        <img src="asset/PHOTO-2025-01-16-12-16-52.jpg" alt="John Doe" class="w-full h-72 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Momoh Mustapha Kamara</h3>
                        <p class="text-lg text-indigo-600 dark:text-indigo-400 mb-4">CEO & Founder</p>
                        <p class="text-gray-600 dark:text-gray-300">
                        <div class="mt-6 flex space-x-4">
                            <a href="https://www.linkedin.com/in/momoh-mustapha-kamara-0632b6211/" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Team Member Card -->
                <div class="group bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <div class="relative">
                        <img src="asset/med.JPG" alt="Jm sandi" class="w-full h-72 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">JM Sandi</h3>
                        <p class="text-lg text-indigo-600 dark:text-indigo-400 mb-4">CTO</p>
                        <p class="text-gray-600 dark:text-gray-300">Full-Stack software Engineer</p>
                        <div class="mt-6 flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Team Member Card -->
                <div class="group bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <div class="relative">
                        <img src="asset/harding.jpg" alt="Mike Johnson" class="w-full h-72 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Abu Bakarr Harding</h3>
                        <p class="text-lg text-indigo-600 dark:text-indigo-400 mb-4">Database Analyst</p>
                        <p class="text-gray-600 dark:text-gray-300">EXPERT IN DESIGNING DATABASE</p>
                        <div class="mt-6 flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Team Member Card -->
                <div class="group bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <div class="relative h-72">
                        <img src="asset/nanana.jpg" alt="Nunana" class="w-full h-full object-cover object-[center_top]">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Ignatus Nunana Dorvi</h3>
                        <p class="text-lg text-indigo-600 dark:text-indigo-400 mb-4">CIO</p>
                        <p class="text-gray-600 dark:text-gray-300">Machine learning and backend development
                        </p>
                        <div class="mt-6 flex space-x-4">
                            <a href="https://www.linkedin.com/in/dorvi-ignatus-nunana-2a944120b?" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Team Member Card -->
                <div class="group bg-white dark:bg-gray-700 rounded-2xl shadow-lg overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <div class="relative h-72">
                        <img src="asset/abass kamara.jpeg" alt="Abass Kamara" class="w-full h-full object-cover object-[center_top]">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Abass Kamara</h3>
                        <p class="text-lg text-indigo-600 dark:text-indigo-400 mb-4">COO</p>
                        <p class="text-gray-600 dark:text-gray-300">Facility Coordinator Consultant</p>
                        <div class="mt-6 flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-indigo-600 transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why We Stand Out -->
    <section class="py-20 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Why We Stand Out</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">Discover what makes us different and why developers choose us.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Feature Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-xl border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Competitive Pricing</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Flexible plans that grow with your needs, making enterprise-grade tools accessible to teams of all sizes.</p>
                </div>

                <!-- Feature Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-xl border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Cutting-edge Technology</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Built on modern architecture with real-time collaboration and AI-powered features.</p>
                </div>

                <!-- Feature Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg transition-all duration-300 hover:-translate-y-2 hover:shadow-xl border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">24/7 Support</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Dedicated support team available around the clock to help you succeed.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Values -->
    <section class="py-20 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-gray-900 dark:to-gray-800">
            <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Our Core Values</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">The principles that guide us in creating exceptional experiences and fostering innovation.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Innovation Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Innovation First</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Pushing boundaries and embracing new technologies to solve complex challenges. We're always exploring what's next.</p>
                </div>

                <!-- Collaboration Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-pink-500 to-rose-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Team Collaboration</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Building strong relationships and fostering a culture of open communication and mutual support.</p>
                </div>

                <!-- Excellence Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Excellence</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Committed to delivering high-quality solutions that exceed expectations and set new industry standards.</p>
                </div>

                <!-- Customer Focus Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Customer Focus</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Putting our customers first and creating solutions that address their real needs and challenges.</p>
                </div>

                <!-- Sustainability Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-lime-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 104 0 2 2 0 012-2v-10a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Sustainability</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Committed to environmental responsibility and sustainable development practices.</p>
                </div>

                <!-- Diversity Card -->
                <div class="group p-8 bg-white dark:bg-gray-800 rounded-xl shadow-md transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border border-gray-100 dark:border-gray-700">
                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mb-6 transform transition-transform group-hover:rotate-6">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Diversity & Inclusion</h3>
                    <p class="text-gray-600 dark:text-gray-300 leading-relaxed">Embracing different perspectives and creating an inclusive environment for all.</p>
                </div>
            </div>

            <!-- Values Statement -->
            <div class="mt-16 text-center">
                <div class="inline-flex items-center justify-center p-1 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500">
                    <div class="px-8 py-4 bg-white dark:bg-gray-800 rounded-full">
                        <p class="text-lg font-semibold bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                            Driving innovation with purpose and passion
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit" 
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                
                <!-- Services -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Our Solutions</h3>
                <ul class="space-y-3">
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Software Development</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Healthcare IT</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Education Technology</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Data Analysis</a></li>
                    <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Security Solutions</a></li>
                </ul>
            </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved..
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <script src="js/main.js"></script>
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>
</body>
</html> 