<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Products - PTech Innovation | Innovative Technology Solutions</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover PTech Innovation's comprehensive suite of technology products including E-learning platforms, EMR systems, telemedicine solutions, and more.">
    <meta name="keywords" content="PTech products, E-learning platform, EMR system, telemedicine, student portal, supply chain management, African tech solutions">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Our Products - PTech Innovation">
    <meta property="og:description" content="Innovative technology products designed for African businesses and institutions.">
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/products.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Our Products - PTech Innovation">
    <meta name="twitter:description" content="Comprehensive technology solutions for healthcare, education, and business.">
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/products.html">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Our Products - PTech Innovation",
      "description": "Comprehensive suite of technology products and solutions",
      "url": "https://ptechin.com/products.html",
      "mainEntity": {
        "@type": "Organization",
        "name": "PTech Innovation",
        "url": "https://ptechin.com"
      }
    }
    </script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.1s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-contact {
            animation: whatsappPulse 2s ease-in-out infinite;
            transition: transform 0.3s ease;
            background-color: #25d366 !important;
            border-radius: 50%;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .floating-contact:hover {
            transform: scale(1.2);
            background-color: #22c15e !important;
        }

        .dark .floating-contact {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .dark .floating-contact:hover {
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4);
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) {
            display: block;
        }

        #mobile-menu.hidden {
            display: none;
        }

        /* Product card animations */
        .product-card {
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .product-image {
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        /* Category filter styles */
        .filter-btn {
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        /* Progress bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 9999;
            transition: width 0.3s ease;
        }

        /* Fade in animation */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Montserrat:wght@400;600&family=Open+Sans:wght@400;600&family=Playfair+Display:wght@400;600&family=Roboto:wght@400;500&family=Source+Sans+Pro:wght@400;600&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>

<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Progress bar -->
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section - Left aligned -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="products.html" class="nav-link text-indigo-600 dark:text-indigo-400 font-semibold">Products</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-gray-600 dark:text-gray-300">Services</a>
                <a href="products.html" class="block text-indigo-600 dark:text-indigo-400 font-semibold">Products</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="fade-in">
                    <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                        Our Products
                    </h1>
                    <p class="mt-3 max-w-md mx-auto text-lg text-gray-200 sm:text-xl md:mt-5 md:max-w-3xl">
                        Innovative technology solutions designed to transform African businesses and communities
                    </p>
                    <div class="mt-10">
                        <a href="#products-grid" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 transition-colors">
                            Explore Our Solutions
                            <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories Filter -->
    <section class="py-8 bg-white dark:bg-gray-800 sticky top-20 z-40 border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-wrap justify-center gap-4">
                <button class="filter-btn active px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" data-filter="all">
                    All Products
                </button>
                <button class="filter-btn px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" data-filter="healthcare">
                    Healthcare
                </button>
                <button class="filter-btn px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" data-filter="education">
                    Education
                </button>
                <button class="filter-btn px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" data-filter="business">
                    Business
                </button>
                <button class="filter-btn px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" data-filter="development">
                    Development
                </button>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section id="products-grid" class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="products-container">

                <!-- E-learning Platform -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="education">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/E-learning.jpeg" alt="E-learning Platform" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Education</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">E-Learning Platform</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Comprehensive online learning management system designed for African educational institutions. Features include course management, student tracking, and interactive learning tools.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">LMS</span>
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Interactive</span>
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Mobile-Ready</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Production Ready
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- EMR System -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="healthcare">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/EMR.jpeg" alt="Electronic Medical Records" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">Healthcare</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Electronic Medical Records (EMR)</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Secure and comprehensive EMR system for healthcare facilities. Streamlines patient data management, appointment scheduling, and medical history tracking.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">HIPAA Compliant</span>
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">Secure</span>
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">Cloud-Based</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Production Ready
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Telemedicine Platform -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="healthcare">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Telemedicine.jpeg" alt="Telemedicine Platform" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">Healthcare</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Telemedicine Platform</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Advanced telemedicine solution enabling remote consultations, video calls, and digital prescriptions. Perfect for expanding healthcare access in remote areas.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">Video Calls</span>
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">Remote Care</span>
                            <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-2 py-1 rounded text-sm">Digital Rx</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Production Ready
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Student Portal -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="education">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Student portal.jpeg" alt="Student Portal" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Education</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Student Portal</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Comprehensive student management system with grade tracking, course enrollment, fee management, and communication tools for educational institutions.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Grade Tracking</span>
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Enrollment</span>
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Fee Management</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Production Ready
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Supply Chain Management -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="business">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Supply chain management.jpeg" alt="Supply Chain Management" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">Business</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Supply Chain Management</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            End-to-end supply chain management solution with inventory tracking, vendor management, and logistics optimization for African businesses.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Inventory</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Logistics</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Analytics</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Production Ready
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Web Development Services -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="development">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Web Development.jpeg" alt="Web Development" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">Development</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Web Development Services</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Custom web development solutions including responsive websites, web applications, and e-commerce platforms tailored for African businesses.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Responsive</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">E-commerce</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Custom</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Software Development -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="development">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Software developmet.jpeg" alt="Software Development" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">Development</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Custom Software Development</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Bespoke software solutions designed to meet specific business requirements. From desktop applications to enterprise systems.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Custom</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Enterprise</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Scalable</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Apps Development -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="development">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Apps development.jpeg" alt="Mobile App Development" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">Development</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Mobile App Development</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Native and cross-platform mobile applications for iOS and Android. From concept to deployment with ongoing support and maintenance.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">iOS</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Android</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Cross-Platform</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Data Analysis & AI -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="business">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Data analysis and AI.jpeg" alt="Data Analysis & AI" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">Business</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Data Analysis & AI Solutions</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Advanced data analytics and artificial intelligence solutions to help businesses make data-driven decisions and automate processes.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">AI/ML</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Analytics</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Automation</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Database Development -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="development">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Database Development.jpeg" alt="Database Development" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-medium">Development</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Database Development & Management</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Professional database design, development, and management services. From small business databases to enterprise-level data warehouses.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">SQL</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">NoSQL</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Cloud</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Data Security -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="business">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/Data security.jpeg" alt="Data Security Solutions" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">Business</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Data Security Solutions</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Comprehensive cybersecurity solutions including data encryption, security audits, and compliance management to protect your business assets.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Encryption</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Compliance</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Audits</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

                <!-- IT Consulting -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in" data-category="business">
                    <div class="relative h-48 overflow-hidden">
                        <img src="asset/IT consunting.jpeg" alt="IT Consulting Services" class="product-image w-full h-full object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">Business</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">IT Consulting Services</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Strategic IT consulting to help businesses optimize their technology infrastructure, digital transformation, and technology adoption strategies.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Strategy</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Digital Transform</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Optimization</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Available Now
                                </span>
                            </div>
                            <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                                Learn More
                            </button>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Product Statistics Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Impact</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Transforming businesses across Africa with innovative technology solutions</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">50+</div>
                    <div class="text-gray-600 dark:text-gray-300">Projects Delivered</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">25+</div>
                    <div class="text-gray-600 dark:text-gray-300">Happy Clients</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">10+</div>
                    <div class="text-gray-600 dark:text-gray-300">Product Solutions</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-indigo-600 dark:text-indigo-400 mb-2">99%</div>
                    <div class="text-gray-600 dark:text-gray-300">Client Satisfaction</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-white mb-8">Ready to Transform Your Business?</h2>
                <p class="text-xl text-gray-200 mb-8">Let's discuss how our products can help you achieve your goals</p>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="contact.html" class="inline-block px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                        Get Started Today
                    </a>
                    <a href="#products-grid" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-indigo-600 transition-colors">
                        View All Products
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit"
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z">
                                </path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="products.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Products</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                <!-- Products -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Our Products</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">E-Learning Platform</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">EMR System</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Telemedicine</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Student Portal</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Supply Chain Management</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved.
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>

    <!-- JavaScript -->
    <script src="js/main.js"></script>

    <script>
        // Product filtering functionality
        document.addEventListener('DOMContentLoaded', function() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const productCards = document.querySelectorAll('.product-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    button.classList.add('active');

                    const filter = button.getAttribute('data-filter');

                    productCards.forEach(card => {
                        if (filter === 'all' || card.getAttribute('data-category') === filter) {
                            card.style.display = 'block';
                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0)';
                            }, 100);
                        } else {
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                card.style.display = 'none';
                            }, 300);
                        }
                    });
                });
            });

            // Fade in animation on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });

            // Progress bar
            window.addEventListener('scroll', () => {
                const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                const scrolled = (winScroll / height) * 100;
                document.getElementById("progressBar").style.width = scrolled + "%";
            });
        });
    </script>
</body>
</html>
