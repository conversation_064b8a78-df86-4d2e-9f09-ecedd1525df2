<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Products - PTech Innovation | School & Hospital Management Systems</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover PTech Innovation's flagship products: Beycenter School Management Dashboard and Medtech Health Platform - comprehensive solutions for education and healthcare.">
    <meta name="keywords" content="school management system, hospital management system, Beycenter, Medtech Health, education software, healthcare software, African tech solutions">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Our Products - PTech Innovation">
    <meta property="og:description" content="Beycenter School Management Dashboard and Medtech Health Platform - innovative solutions for education and healthcare."
    <meta property="og:image" content="asset/PowerTech_Logo-removebg-preview.png">
    <meta property="og:url" content="https://ptechin.com/products.html">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Our Products - PTech Innovation">
    <meta name="twitter:description" content="Beycenter School Management Dashboard and Medtech Health Platform - transforming education and healthcare."
    <meta name="twitter:image" content="asset/PowerTech_Logo-removebg-preview.png">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://ptechin.com/products.html">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Our Products - PTech Innovation",
      "description": "Beycenter School Management Dashboard and Medtech Health Platform",
      "url": "https://ptechin.com/products.html",
      "mainEntity": {
        "@type": "Organization",
        "name": "PTech Innovation",
        "url": "https://ptechin.com"
      }
    }
    </script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        @keyframes logoFloat {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes textSlide {
            0% {
                transform: translateY(100%);
                opacity: 0;
            }
            20%, 80% {
                transform: translateY(0);
                opacity: 1;
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover {
            transform: scale(1.05);
        }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.1s ease;
        }

        .logo-text:hover {
            color: #4f46e5;
        }

        .nav-container {
            overflow: hidden;
        }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-contact {
            animation: whatsappPulse 2s ease-in-out infinite;
            transition: transform 0.3s ease;
            background-color: #25d366 !important;
            border-radius: 50%;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .floating-contact:hover {
            transform: scale(1.2);
            background-color: #22c15e !important;
        }

        .dark .floating-contact {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }

        .dark .floating-contact:hover {
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.4);
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Mobile menu transition */
        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) {
            display: block;
        }

        #mobile-menu.hidden {
            display: none;
        }

        /* Product card animations */
        .product-card {
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .product-image {
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
            transform: scale(1.05);
        }

        /* Category filter styles */
        .filter-btn {
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
        }

        /* Progress bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            z-index: 9999;
            transition: width 0.3s ease;
        }

        /* Fade in animation */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&family=Montserrat:wght@400;600&family=Open+Sans:wght@400;600&family=Playfair+Display:wght@400;600&family=Roboto:wght@400;500&family=Source+Sans+Pro:wght@400;600&display=swap" rel="stylesheet">
    
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // Add any custom colors here
                    }
                }
            }
        }
    </script>
</head>

<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Progress bar -->
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section - Left aligned -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="products.html" class="nav-link text-indigo-600 dark:text-indigo-400 font-semibold">Products</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-gray-600 dark:text-gray-300">Services</a>
                <a href="products.html" class="block text-indigo-600 dark:text-indigo-400 font-semibold">Products</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-indigo-600 to-purple-600 relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="fade-in">
                    <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                        Our Products
                    </h1>
                    <p class="mt-3 max-w-md mx-auto text-lg text-gray-200 sm:text-xl md:mt-5 md:max-w-3xl">
                        Flagship products transforming education and healthcare across Africa
                    </p>
                    <div class="mt-10">
                        <a href="#products-grid" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 transition-colors">
                            Explore Our Solutions
                            <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Overview -->
    <section class="py-12 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Our Flagship Products</h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                We've developed two comprehensive management systems that are currently transforming institutions across Africa.
                Each product is built with cutting-edge technology and designed specifically for African contexts.
            </p>
        </div>
    </section>

    <!-- Products Grid -->
    <section id="products-grid" class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12" id="products-container">

                <!-- Beycenter School Management Dashboard -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative">
                        <!-- Product Header -->
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">Education</span>
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Live Product</span>
                            </div>
                            <h3 class="text-2xl font-bold mb-2">Beycenter School Management Dashboard</h3>
                            <p class="text-blue-100">
                                Comprehensive school management system streamlining administrative tasks, student records, and academic operations.
                            </p>
                        </div>

                        <!-- Screenshot Gallery -->
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="relative group cursor-pointer">
                                    <img src="products/Beycenter/Screenshot 2025-08-01 at 7.27.55 PM.png" alt="Beycenter Dashboard" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.09 PM.png" alt="Student Management" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.27 PM.png" alt="Academic Records" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.52 PM.png" alt="Reports & Analytics" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                            </div>

                            <!-- Features -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4>
                                <div class="grid grid-cols-2 gap-2 text-sm">
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Student Management
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Grade Management
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Attendance Tracking
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Fee Management
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Reports & Analytics
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Parent Portal
                                    </div>
                                </div>
                            </div>

                            <!-- Technology Stack -->
                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">React</span>
                                <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Node.js</span>
                                <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">MongoDB</span>
                                <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">Cloud-Based</span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                    Request Demo
                                </button>
                                <button class="flex-1 border border-indigo-600 text-indigo-600 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 px-6 py-3 rounded-lg font-semibold transition-colors">
                                    View Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medtech Health Platform -->
                <div class="product-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative">
                        <!-- Product Header -->
                        <div class="bg-gradient-to-r from-red-600 to-pink-600 p-6 text-white">
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">Healthcare</span>
                                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Live Product</span>
                            </div>
                            <h3 class="text-2xl font-bold mb-2">Medtech Health Platform</h3>
                            <p class="text-red-100">
                                Advanced hospital management system revolutionizing healthcare delivery with comprehensive patient care and administrative tools.
                            </p>
                        </div>

                        <!-- Screenshot Gallery -->
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="relative group cursor-pointer">
                                    <img src="products/Medtech Health platform/Screenshot 2025-08-01 at 7.33.19 PM.png" alt="Medtech Dashboard" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Medtech Health platform/Screenshot 2025-08-01 at 7.33.48 PM.png" alt="Patient Management" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Medtech Health platform/Screenshot 2025-08-01 at 7.34.16 PM.png" alt="Medical Records" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                                <div class="relative group cursor-pointer">
                                    <img src="products/Medtech Health platform/Screenshot 2025-08-01 at 7.34.46 PM.png" alt="Appointment System" class="w-full h-32 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow">
                                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-colors"></div>
                                </div>
                            </div>

                            <!-- Features -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4>
                                <div class="grid grid-cols-2 gap-2 text-sm">
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Patient Records
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Appointment Scheduling
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Billing & Insurance
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Inventory Management
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Staff Management
                                    </div>
                                    <div class="flex items-center text-gray-600 dark:text-gray-300">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                        </svg>
                                        Analytics & Reports
                                    </div>
                                </div>
                            </div>

                            <!-- Technology Stack -->
                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">React</span>
                                <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">Node.js</span>
                                <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">PostgreSQL</span>
                                <span class="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 px-3 py-1 rounded-full text-sm">HIPAA Compliant</span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button class="flex-1 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                    Request Demo
                                </button>
                                <button class="flex-1 border border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 px-6 py-3 rounded-lg font-semibold transition-colors">
                                    View Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </section>

    <!-- Product Statistics Section -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Product Impact</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Real results from our flagship products transforming education and healthcare</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">2</div>
                    <div class="text-gray-600 dark:text-gray-300">Live Products</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-red-600 dark:text-red-400 mb-2">5+</div>
                    <div class="text-gray-600 dark:text-gray-300">Active Institutions</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">1000+</div>
                    <div class="text-gray-600 dark:text-gray-300">Users Served</div>
                </div>
                <div class="text-center fade-in">
                    <div class="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">24/7</div>
                    <div class="text-gray-600 dark:text-gray-300">System Uptime</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-white mb-8">Ready to Transform Your Institution?</h2>
                <p class="text-xl text-gray-200 mb-8">Experience the power of Beycenter and Medtech Health Platform</p>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="contact.html" class="inline-block px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                        Request Demo
                    </a>
                    <a href="#products-grid" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-indigo-600 transition-colors">
                        View Products
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <!-- Newsletter Section -->
        <div class="border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex flex-col md:flex-row items-center justify-between">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-2xl font-bold mb-2">Stay in the loop</h3>
                        <p class="text-gray-400">Get the latest updates and news directly in your inbox.</p>
                    </div>
                    <div class="w-full md:w-auto">
                        <form class="flex gap-2 newsletter-form" novalidate>
                            <input type="email" name="email" placeholder="Enter your email" required
                                class="px-4 py-3 rounded-lg bg-gray-800 border border-gray-700 focus:ring-2 focus:ring-indigo-500 focus:border-transparent flex-grow md:w-80">
                            <button type="submit"
                                class="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 rounded-lg font-semibold transition-colors">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
                <!-- Company Info -->
                <div>
                    <h3 class="text-xl font-bold mb-4">PTech Innovation</h3>
                    <p class="text-gray-400 mb-4">Empowering Africa's digital transformation through innovative technology solutions.</p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/company/ptechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z">
                                </path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">YouTube</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                            </svg>
                        </a>
                        <a href="https://www.facebook.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">Facebook</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.77 7.46H14.5v-1.9c0-.9.6-1.1 1-1.1h3V.5h-4.33C10.24.5 9.5 3.44 9.5 5.32v2.15h-3v4h3v12h5v-12h3.85l.42-4z"/>
                            </svg>
                        </a>
                        <a href="https://X.com/PTechin24" class="text-gray-400 hover:text-indigo-500 transition-colors">
                            <span class="sr-only">X (Twitter)</span>
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Home</a></li>
                        <li><a href="about.html" class="text-gray-400 hover:text-indigo-500 transition-colors">About Us</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Services</a></li>
                        <li><a href="products.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Products</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Contact</a></li>
                        <li><a href="legal.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Legal</a></li>
                    </ul>
                </div>

                <!-- Products -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Our Products</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Beycenter School Management</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-indigo-500 transition-colors">Medtech Health Platform</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Custom Development</a></li>
                        <li><a href="services.html" class="text-gray-400 hover:text-indigo-500 transition-colors">IT Consulting</a></li>
                        <li><a href="contact.html" class="text-gray-400 hover:text-indigo-500 transition-colors">Request Demo</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                    <ul class="space-y-3">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="text-gray-400">34D Mamabridge Kissy freetown</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            <span class="text-gray-400">+447360680621</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-gray-400 text-sm">
                        © 2025 PTech Innovation. All rights reserved.
                    </div>
                    <div class="flex space-x-6">
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Privacy Policy</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Terms of Service</a>
                        <a href="legal.html" class="text-gray-400 hover:text-indigo-500 text-sm transition-colors">Cookie Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button id="contactButton" class="floating-contact whatsapp-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>

    <!-- JavaScript -->
    <script src="js/main.js"></script>

    <script>
        // Product showcase functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Fade in animation on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });

            // Progress bar
            window.addEventListener('scroll', () => {
                const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                const scrolled = (winScroll / height) * 100;
                document.getElementById("progressBar").style.width = scrolled + "%";
            });

            // Image gallery hover effects
            const galleryImages = document.querySelectorAll('.product-card img');
            galleryImages.forEach(img => {
                img.addEventListener('click', () => {
                    // Could add lightbox functionality here
                    console.log('Image clicked:', img.alt);
                });
            });
        });
    </script>
</body>
</html>
