<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Management Dashboard - PTech Innovation</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Beycenter School Management Dashboard - Comprehensive school management system streamlining administrative tasks, student records, and academic operations.">
    <meta name="keywords" content="Beycenter, school management system, student management, grade tracking, attendance, fee management, education software">
    <meta name="author" content="PTech Innovation">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Beycenter School Management Dashboard - PTech Innovation">
    <meta property="og:description" content="Comprehensive school management system for educational institutions.">
    <meta property="og:image" content="products/Beycenter/Screenshot 2025-08-01 at 7.27.55 PM.png">
    <meta property="og:url" content="https://ptechin.com/beycenter.html">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="asset/PowerTech_Logo-removebg-preview.png">
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Reuse consistent styles */
        @keyframes logoFloat {
            0% { transform: translateY(100%); opacity: 0; }
            20%, 80% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100%); opacity: 0; }
        }

        @keyframes textSlide {
            0% { transform: translateY(100%); opacity: 0; }
            20%, 80% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-100%); opacity: 0; }
        }

        .logo-img {
            animation: logoFloat 4s ease-in-out infinite;
            transition: transform 0.3s ease;
        }

        .logo-img:hover { transform: scale(1.05); }

        .logo-text {
            animation: textSlide 4s ease-in-out infinite;
            transition: color 0.1s ease;
        }

        .logo-text:hover { color: #4f46e5; }

        .logo-container {
            background: white;
            padding: 4px;
            border-radius: 8px;
        }

        @keyframes whatsappPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-contact {
            animation: whatsappPulse 2s ease-in-out infinite;
            transition: transform 0.3s ease;
            background-color: #25d366 !important;
            border-radius: 50%;
            padding: 15px;
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .floating-contact:hover {
            transform: scale(1.2);
            background-color: #22c15e !important;
        }

        .nav-link {
            position: relative;
            padding: 0.5rem 0;
            transition: color 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: currentColor;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after { width: 100%; }

        #mobile-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            width: 100%;
            background-color: white;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            opacity: 0;
            transform: translateY(-1rem);
            pointer-events: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }

        .dark #mobile-menu {
            background-color: #1f2937;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
        }

        #mobile-menu.show {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        #mobile-menu:not(.hidden) { display: block; }
        #mobile-menu.hidden { display: none; }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #2563eb, #3b82f6);
            z-index: 9999;
            transition: width 0.3s ease;
        }

        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .screenshot-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .modal-content {
            margin: auto;
            display: block;
            width: 80%;
            max-width: 700px;
            max-height: 80%;
            object-fit: contain;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
        }

        .close:hover,
        .close:focus {
            color: #bbb;
            text-decoration: none;
            cursor: pointer;
        }
    </style>

    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {}
                }
            }
        }
    </script>
</head>

<body class="font-['Inter'] bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Progress bar -->
    <div class="progress-bar" id="progressBar"></div>
    
    <!-- Navigation -->
    <nav class="bg-white dark:bg-gray-800 shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-20">
                <!-- Logo section -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <div class="logo-container w-16 h-16">
                            <img src="asset/PowerTech_Logo-removebg-preview.png" alt="PTech Innovation Logo" class="h-full w-auto logo-img object-contain">
                        </div>
                        <span class="text-lg font-bold text-indigo-600 dark:text-indigo-400 logo-text">PTech Innovation</span>
                    </a>
                </div>
    
                <!-- Right side container for menu and controls -->
                <div class="flex items-center justify-end flex-1 space-x-8">
                    <!-- Desktop menu items -->
                    <div class="hidden md:flex items-center space-x-8 ml-auto">
                        <a href="index.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Home</a>
                        <a href="about.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">About</a>
                        <a href="services.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Services</a>
                        <a href="products.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Products</a>
                        <a href="charity.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Charity</a>
                        <a href="legal.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Legal</a>
                        <a href="contact.html" class="nav-link text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">Contact</a>
                    </div>
    
                    <!-- Controls section -->
                    <div class="flex items-center space-x-4">
                        <button id="dark-mode-toggle" class="p-2 rounded-lg text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>
    
                        <!-- Mobile menu button -->
                        <button id="mobile-menu-button" type="button" class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="menu-items">
                <a href="index.html" class="block text-gray-600 dark:text-gray-300">Home</a>
                <a href="about.html" class="block text-gray-600 dark:text-gray-300">About</a>
                <a href="services.html" class="block text-gray-600 dark:text-gray-300">Services</a>
                <a href="products.html" class="block text-gray-600 dark:text-gray-300">Products</a>
                <a href="charity.html" class="block text-gray-600 dark:text-gray-300">Charity</a>
                <a href="legal.html" class="block text-gray-600 dark:text-gray-300">Legal</a>
                <a href="contact.html" class="block text-gray-600 dark:text-gray-300">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-24 pb-12 bg-gradient-to-r from-blue-600 to-indigo-600 relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="lg:flex lg:items-center lg:justify-between">
                    <div class="lg:w-1/2">
                        <div class="flex items-center mb-4">
                            <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium mr-3">Education</span>
                            <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">Live Product</span>
                        </div>
                        <h1 class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
                            School Management Dashboard
                        </h1>
                        <p class="mt-3 max-w-md text-lg text-blue-100 sm:text-xl md:mt-5 md:max-w-3xl">
                            Comprehensive school management system streamlining administrative tasks, student records, and academic operations for educational institutions across Africa.
                        </p>
                        <div class="mt-10 flex flex-col sm:flex-row gap-4">
                            <a href="contact.html" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors">
                                Request Demo
                            </a>
                            <a href="products.html" class="inline-flex items-center px-6 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors">
                                View All Products
                            </a>
                        </div>
                    </div>
                    <div class="mt-10 lg:mt-0 lg:w-1/2">
                        <div class="relative">
                            <img src="products/Beycenter/Screenshot 2025-08-01 at 7.27.55 PM.png" alt="Beycenter Dashboard" class="w-full rounded-lg shadow-2xl">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Overview -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-2 lg:gap-12 lg:items-center">
                <div class="fade-in">
                    <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">Revolutionizing School Management</h2>
                    <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                        OUR SMS is a comprehensive school management dashboard designed specifically for African educational institutions.
                        Our platform streamlines administrative processes, enhances communication, and provides powerful analytics to help schools operate more efficiently.
                    </p>
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">500+</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">Students Managed</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">50+</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">Teachers</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">10+</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">Schools</div>
                        </div>
                        <div class="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                            <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">99.9%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">Uptime</div>
                        </div>
                    </div>
                </div>
                <div class="mt-10 lg:mt-0 fade-in">
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-8 text-white">
                        <h3 class="text-xl font-bold mb-4">Key Benefits</h3>
                        <ul class="space-y-3">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                </svg>
                                Reduce administrative workload by 60%
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                </svg>
                                Improve parent-school communication
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                </svg>
                                Real-time academic performance tracking
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                </svg>
                                Automated fee management and billing
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 mr-3 text-green-300" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"></path>
                                </svg>
                                Comprehensive reporting and analytics
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Gallery -->
    <section class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 fade-in">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Explore Features</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Click on any screenshot to view it in detail</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Dashboard Overview -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.27.55 PM.png', 'Main Dashboard Overview')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.27.55 PM.png" alt="Main Dashboard" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Main Dashboard Overview</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            The central hub displaying key metrics, recent activities, and quick access to all major functions.
                            Get a comprehensive view of your school's performance at a glance.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Analytics</span>
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Quick Access</span>
                            <span class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-sm">Real-time Data</span>
                        </div>
                    </div>
                </div>

                <!-- Student Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.28.09 PM.png', 'Student Management System')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.09 PM.png" alt="Student Management" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Student Management System</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Comprehensive student information management including enrollment, personal details, academic history,
                            and parent contact information. Easily search, filter, and manage student records.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Enrollment</span>
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Records</span>
                            <span class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded text-sm">Search & Filter</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- More Features -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                <!-- Academic Records -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.28.27 PM.png', 'Academic Records & Grading')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.27 PM.png" alt="Academic Records" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Academic Records & Grading</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Comprehensive grade management system with subject-wise tracking, term reports,
                            and automated GPA calculations. Generate report cards and academic transcripts instantly.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Grade Tracking</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">Report Cards</span>
                            <span class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded text-sm">GPA Calculation</span>
                        </div>
                    </div>
                </div>

                <!-- Reports & Analytics -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.28.52 PM.png', 'Reports & Analytics Dashboard')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.28.52 PM.png" alt="Reports & Analytics" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Reports & Analytics Dashboard</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Powerful analytics and reporting tools providing insights into student performance,
                            attendance trends, financial summaries, and institutional metrics for data-driven decisions.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Analytics</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Performance Metrics</span>
                            <span class="bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200 px-2 py-1 rounded text-sm">Data Insights</span>
                        </div>
                    </div>
                </div>

                <!-- Attendance Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.29.06 PM.png', 'Attendance Management System')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.29.06 PM.png" alt="Attendance Management" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Attendance Management System</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Streamlined attendance tracking with daily, weekly, and monthly views.
                            Automated notifications to parents for absences and comprehensive attendance reports.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-sm">Daily Tracking</span>
                            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-sm">Notifications</span>
                            <span class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded text-sm">Reports</span>
                        </div>
                    </div>
                </div>

                <!-- Class Management -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden fade-in">
                    <div class="relative group cursor-pointer" onclick="openModal('products/Beycenter/Screenshot 2025-08-01 at 7.29.38 PM.png', 'Class & Schedule Management')">
                        <img src="products/Beycenter/Screenshot 2025-08-01 at 7.29.38 PM.png" alt="Class Management" class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <svg class="w-12 h-12 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Class & Schedule Management</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-4">
                            Organize classes, manage timetables, assign teachers to subjects,
                            and handle room allocations. Create flexible schedules that adapt to your school's needs.
                        </p>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 px-2 py-1 rounded text-sm">Timetables</span>
                            <span class="bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 px-2 py-1 rounded text-sm">Room Allocation</span>
                            <span class="bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 px-2 py-1 rounded text-sm">Teacher Assignment</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Stack -->
    <section class="py-16 bg-white dark:bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12 fade-in">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Built with Modern Technology</h2>
                <p class="text-lg text-gray-600 dark:text-gray-300">Reliable, scalable, and secure technology stack</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center fade-in">
                    <div class="bg-blue-100 dark:bg-blue-900/20 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">React</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Frontend Framework</p>
                </div>

                <div class="text-center fade-in">
                    <div class="bg-green-100 dark:bg-green-900/20 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Node.js</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Backend Runtime</p>
                </div>

                <div class="text-center fade-in">
                    <div class="bg-purple-100 dark:bg-purple-900/20 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">MongoDB</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Database</p>
                </div>

                <div class="text-center fade-in">
                    <div class="bg-indigo-100 dark:bg-indigo-900/20 w-16 h-16 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7v10c0 5.55 3.84 9.739 9 11 5.16-1.261 9-5.45 9-11V7l-10-5z"/>
                        </svg>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">Cloud</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-300">Hosting</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-white mb-8">Ready to Transform Your School?</h2>
                <p class="text-xl text-blue-100 mb-8">Join schools across Africa using Beycenter to streamline their operations</p>
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="contact.html" class="inline-block px-8 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
                        Request Demo
                    </a>
                    <a href="products.html" class="inline-block px-8 py-3 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-blue-600 transition-colors">
                        View All Products
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshot Modal -->
    <div id="screenshotModal" class="screenshot-modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
        <div id="caption" class="text-center text-white mt-4 text-lg"></div>
    </div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.link/eg8s8c" target="_blank">
        <button class="floating-contact">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="white" viewBox="0 0 24 24">
                <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
        </button>
    </a>

    <!-- JavaScript -->
    <script src="js/main.js"></script>

    <script>
        // Modal functionality
        function openModal(imageSrc, caption) {
            const modal = document.getElementById('screenshotModal');
            const modalImg = document.getElementById('modalImage');
            const captionText = document.getElementById('caption');

            modal.style.display = 'block';
            modalImg.src = imageSrc;
            captionText.innerHTML = caption;
        }

        function closeModal() {
            document.getElementById('screenshotModal').style.display = 'none';
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            const modal = document.getElementById('screenshotModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // Fade in animation on scroll
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });

            // Progress bar
            window.addEventListener('scroll', () => {
                const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
                const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
                const scrolled = (winScroll / height) * 100;
                document.getElementById("progressBar").style.width = scrolled + "%";
            });
        });
    </script>
</body>
</html>
